include("CacheJSON.jl")
using .CacheJSON

println("=== BENCHMARK EXTRACTION EN MASSE ULTRA-PERFORMANTE ===")
println("🎯 OBJECTIF: Démontrer l'extraction ultra-rapide en gros volumes")
println("🚀 TECHNIQUES: Index O(1) + Threading massif + Batch processing")
println("="^70)

# Configuration ultra-performance
CacheJSON.configure_cache(
    max_parties = 2000,
    max_files = 1,
    threading = true,
    stats = false
)

filepath = "C:\\Users\\<USER>\\Desktop\\modules\\partie\\dataset.json"

# Test 1: Extraction individuelle (méthode de base)
println("\n📊 TEST 1: EXTRACTION INDIVIDUELLE (méthode de base)")
partie_ids_small = collect(1:10)

start_time = time()
parties_individual = []
for id in partie_ids_small
    partie = CacheJSON.get_partie_cached(id, filepath)
    push!(parties_individual, partie)
end
end_time = time()

individual_time = end_time - start_time
individual_speed = length(partie_ids_small) / individual_time

println("   $(length(partie_ids_small)) parties en: $(round(individual_time, digits=3))s")
println("   Vitesse: $(round(individual_speed, digits=1)) parties/sec")

# Test 2: Extraction en masse (nouvelle méthode ultra-rapide)
println("\n🚀 TEST 2: EXTRACTION EN MASSE ULTRA-RAPIDE")
CacheJSON.clear_all_caches()  # Test propre

start_time = time()
parties_batch = CacheJSON.get_parties_batch_ultra_fast(partie_ids_small, filepath)
end_time = time()

batch_time = end_time - start_time
batch_speed = length(partie_ids_small) / batch_time
acceleration = batch_speed / individual_speed

println("   $(length(partie_ids_small)) parties en: $(round(batch_time, digits=3))s")
println("   Vitesse: $(round(batch_speed, digits=1)) parties/sec")
println("   Accélération: $(round(acceleration, digits=1))x plus rapide")

# Test 3: Volume moyen (100 parties)
println("\n🔥 TEST 3: VOLUME MOYEN (100 parties)")
partie_ids_medium = collect(1:100)

start_time = time()
parties_medium = CacheJSON.get_parties_batch_ultra_fast(partie_ids_medium, filepath)
end_time = time()

medium_time = end_time - start_time
medium_speed = 100 / medium_time

println("   100 parties en: $(round(medium_time, digits=3))s")
println("   Vitesse: $(round(medium_speed, digits=1)) parties/sec")

# Test 4: Gros volume (1000 parties)
println("\n⚡ TEST 4: GROS VOLUME (1000 parties)")
partie_ids_large = collect(1:1000)

start_time = time()
parties_large = CacheJSON.get_parties_batch_ultra_fast(partie_ids_large, filepath)
end_time = time()

large_time = end_time - start_time
large_speed = 1000 / large_time

println("   1000 parties en: $(round(large_time, digits=3))s")
println("   Vitesse: $(round(large_speed, digits=1)) parties/sec")

# Test 5: Très gros volume (10000 parties)
println("\n🌟 TEST 5: TRÈS GROS VOLUME (10000 parties)")
partie_ids_huge = collect(1:10000)

start_time = time()
parties_huge = CacheJSON.get_parties_batch_ultra_fast(partie_ids_huge, filepath)
end_time = time()

huge_time = end_time - start_time
huge_speed = 10000 / huge_time

println("   10000 parties en: $(round(huge_time, digits=3))s")
println("   Vitesse: $(round(huge_speed, digits=1)) parties/sec")

# Test 6: Extraction mains en masse
println("\n🎲 TEST 6: EXTRACTION MAINS EN MASSE (1000 parties)")
start_time = time()
mains_batch = CacheJSON.get_mains_batch_ultra_fast(collect(1:1000), filepath)
end_time = time()

mains_time = end_time - start_time
total_mains = sum(length(mains) for mains in mains_batch)
mains_speed = total_mains / mains_time

println("   1000 parties, $total_mains mains en: $(round(mains_time, digits=3))s")
println("   Vitesse: $(round(mains_speed, digits=1)) mains/sec")

# Résumé final
println("\n" * "="^70)
println("📋 RÉSUMÉ FINAL - EXTRACTION EN MASSE ULTRA-PERFORMANTE")
println("="^70)
println("🎯 Objectif: Extraction ultra-rapide en gros volumes")
println("📊 Extraction individuelle: $(round(individual_speed, digits=1)) parties/sec")
println("🚀 Extraction batch (10): $(round(batch_speed, digits=1)) parties/sec ($(round(acceleration, digits=1))x)")
println("🔥 Volume moyen (100): $(round(medium_speed, digits=1)) parties/sec")
println("⚡ Gros volume (1000): $(round(large_speed, digits=1)) parties/sec")
println("🌟 Très gros volume (10000): $(round(huge_speed, digits=1)) parties/sec")
println("🎲 Extraction mains: $(round(mains_speed, digits=1)) mains/sec")

# Évaluation des performances
if huge_speed > 1000
    println("🏆 EXCEPTIONNEL: >1000 parties/sec atteint!")
elseif huge_speed > 500
    println("🥇 EXCELLENT: >500 parties/sec")
elseif huge_speed > 100
    println("🥈 TRÈS BON: >100 parties/sec")
else
    println("🥉 BON: $(round(huge_speed, digits=1)) parties/sec")
end

println("="^70)
