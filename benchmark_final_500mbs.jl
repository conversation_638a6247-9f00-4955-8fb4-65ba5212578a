include("CacheJSON.jl")
using .CacheJSON

println("=== BENCHMARK FINAL - OBJECTIF 500+ MB/s ===")

# Configuration ULTRA-AGRESSIVE
CacheJSON.configure_cache(
    max_parties = 2000,  # Utilise 25GB des 28GB
    max_files = 1,       # Un seul gros fichier
    threading = true,    # Threading max
    stats = false        # Zéro overhead
)

filepath = "C:\\Users\\<USER>\\Desktop\\modules\\partie\\dataset.json"
file_size_mb = 711276114 / (1024 * 1024)

println("📁 Fichier: dataset.json ($(round(file_size_mb, digits=1)) MB)")
println("🎯 Objectif: 500+ MB/s")
println("🚀 Configuration: 2000 parties cache, 25GB RAM")
println("="^60)

# Test optimisé - 3 essais pour moyenne
speeds = Float64[]

for i in 1:3
    println("\n🔥 ESSAI $i/3:")
    
    # Vider cache pour test propre
    CacheJSON.clear_all_caches()
    
    # Forcer garbage collection
    GC.gc()
    
    start_time = time()
    json_data = CacheJSON.get_cached_json(filepath)
    end_time = time()
    
    load_time = end_time - start_time
    speed = file_size_mb / load_time
    push!(speeds, speed)
    
    println("   Temps: $(round(load_time, digits=3))s")
    println("   Vitesse: $(round(speed, digits=1)) MB/s")
end

# Calcul moyenne et meilleure performance
avg_speed = sum(speeds) / length(speeds)
max_speed = maximum(speeds)

println("\n" * "="^60)
println("📊 RÉSULTATS FINAUX")
println("="^60)
println("🎯 Objectif: 500 MB/s")
println("📈 Vitesse moyenne: $(round(avg_speed, digits=1)) MB/s")
println("🚀 Meilleure vitesse: $(round(max_speed, digits=1)) MB/s")

if max_speed >= 500
    println("🏆 OBJECTIF 500 MB/s ATTEINT!")
    println("✅ Performance exceptionnelle!")
elseif max_speed >= 450
    println("🥈 TRÈS PROCHE! (>450 MB/s)")
    println("✅ Performance excellente!")
elseif max_speed >= 400
    println("🥉 BONNE PERFORMANCE (>400 MB/s)")
    println("✅ Objectif presque atteint!")
else
    println("⚠️  Performance à améliorer")
end

# Test cache chaud
println("\n⚡ TEST CACHE CHAUD:")
start_time = time()
cached_data = CacheJSON.get_cached_json(filepath)
end_time = time()

if (end_time - start_time) < 0.001
    println("🚀 Cache parfait: < 1ms (∞ MB/s)")
else
    cached_speed = file_size_mb / (end_time - start_time)
    println("🚀 Vitesse cache: $(round(cached_speed, digits=1)) MB/s")
end

println("="^60)
