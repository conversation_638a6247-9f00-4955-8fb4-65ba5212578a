include("CacheJSON.jl")
using .CacheJSON

println("=== BENCHMARK CACHE JSON ===")

# Test 1: Première lecture (cache froid)
println("1. Première lecture partie 1 (cache froid):")
@time partie1 = get_partie_cached(1)
println("   Mains chargées: $(length(partie1.mains))")

# Test 2: Deuxième lecture (cache chaud)
println("2. Deuxième lecture partie 1 (cache chaud):")
@time partie1_bis = get_partie_cached(1)

# Test 3: Nouvelle partie (cache froid)
println("3. Première lecture partie 2 (cache froid):")
@time partie2 = get_partie_cached(2)
println("   Mains chargées: $(length(partie2.mains))")

# Test 4: Cache chaud partie 2
println("4. Deuxième lecture partie 2 (cache chaud):")
@time partie2_bis = get_partie_cached(2)

# Test 5: Comptage parties
println("5. Comptage parties (avec cache fichier):")
@time nb_parties = count_parties_in_dataset()
println("   Parties trouvées: $nb_parties")

# Statistiques finales
println("\n=== STATISTIQUES CACHE ===")
stats = get_cache_stats()
println("Hit rate parties: $(round(stats.partie_hit_rate * 100, digits=1))%")
println("Hit rate fichiers: $(round(stats.file_hit_rate * 100, digits=1))%")
println("Mémoire estimée: $(round(stats.estimated_memory_mb, digits=1)) MB")
