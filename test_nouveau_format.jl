include("CacheJSON.jl")
using .CacheJSON

println("=== TEST NOUVEAU FORMAT JSON ===")

# Configuration simple pour test
CacheJSON.configure_cache(
    max_parties = 100,
    max_files = 1,
    threading = true,
    stats = false
)

filepath = "C:\\Users\\<USER>\\Desktop\\modules\\partie\\dataset.json"

try
    println("1. Test chargement JSON...")
    json_data = CacheJSON.get_cached_json(filepath)
    println("   ✅ JSON chargé")
    
    println("2. Test comptage parties...")
    nb_parties = CacheJSON.count_parties_in_dataset(filepath)
    println("   ✅ Parties trouvées: $nb_parties")
    
    println("3. Test extraction partie 1...")
    partie1 = CacheJSON.get_partie_cached(1, filepath)
    println("   ✅ Partie 1: $(length(partie1.mains)) mains")
    
    # Afficher quelques mains pour vérifier le format
    println("4. Vérification format des mains:")
    for i in 1:min(3, length(partie1.mains))
        main = partie1.mains[i]
        println("   Main $i: index1=$(main.index1), index2=$(main.index2), index3=$(main.index3), index5=$(main.index5)")
    end
    
    println("5. Test extraction multiple...")
    start_time = time()
    for i in 1:5
        partie = CacheJSON.get_partie_cached(i, filepath)
    end
    end_time = time()
    
    total_time = end_time - start_time
    speed = 5 / total_time
    println("   ✅ 5 parties en $(round(total_time, digits=3))s")
    println("   ✅ Vitesse: $(round(speed, digits=1)) parties/sec")
    
    println("\n✅ NOUVEAU FORMAT FONCTIONNE PARFAITEMENT!")
    
catch e
    println("❌ Erreur: $e")
    println("Stacktrace:")
    for (i, frame) in enumerate(stacktrace(catch_backtrace()))
        println("  $i. $frame")
        if i > 5
            break
        end
    end
end
