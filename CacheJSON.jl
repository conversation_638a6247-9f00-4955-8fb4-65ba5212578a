"""
CacheJSON.jl - Système de mise en cache optimisé pour fichiers JSON volumineux
Utilise LazyJSON.jl + Mmap + Memoize.jl pour performance maximale
Optimisé pour 8 cœurs CPU et 28GB RAM

Auteur: Système de prédiction INDEX5
Date: $(Dates.format(now(), "yyyy-mm-dd HH:MM:SS"))
"""

module CacheJSON

using LazyJSON
using Memoize
using Mmap
using Dates
using Base.Threads

# ============================================================================
# TYPES ET STRUCTURES
# ============================================================================

"""
Structure pour les données d'une main de baccarat
"""
struct MainData
    index1::Int
    index2::String
    index3::String
    index5::String
end

"""
Structure pour une partie complète
"""
struct PartieData
    id::Int
    mains::Vector{MainData}
    metadata::Dict{String, Any}
end

"""
Configuration du cache
"""
struct CacheConfig
    max_parties_cache::Int
    max_file_cache::Int
    enable_threading::Bool
    cache_stats::Bool
end

# Configuration par défaut optimisée pour 28GB RAM
const DEFAULT_CONFIG = CacheConfig(
    100,    # max_parties_cache - Cache pour 100 parties max
    5,      # max_file_cache - Cache pour 5 fichiers JSON max
    true,   # enable_threading - Utiliser les 8 cœurs
    true    # cache_stats - Statistiques de cache
)

# ============================================================================
# VARIABLES GLOBALES ET CACHE
# ============================================================================

# Cache global pour les fichiers JSON chargés
const FILE_CACHE = Dict{String, LazyJSON.Value}()

# Cache global pour les parties converties
const PARTIE_CACHE = Dict{Tuple{String, Int}, PartieData}()

# Statistiques de cache
mutable struct CacheStats
    file_hits::Int
    file_misses::Int
    partie_hits::Int
    partie_misses::Int
    total_memory_mb::Float64
    last_cleanup::DateTime
end

const CACHE_STATS = CacheStats(0, 0, 0, 0, 0.0, now())

# Configuration active
const ACTIVE_CONFIG = Ref(DEFAULT_CONFIG)

# ============================================================================
# FONCTIONS DE CHARGEMENT OPTIMISÉES
# ============================================================================

"""
    load_json_with_mmap(filepath::String) -> LazyJSON.Value

Charge un fichier JSON volumineux avec memory mapping et parsing paresseux.
Optimisé pour fichiers de plusieurs Go.
"""
function load_json_with_mmap(filepath::String)::LazyJSON.Value
    if !isfile(filepath)
        throw(ArgumentError("Fichier non trouvé: $filepath"))
    end
    
    try
        open(filepath, "r") do f
            # Memory mapping pour éviter copie en mémoire
            jsontext = String(Mmap.mmap(f))
            
            # Parsing paresseux - instantané
            return LazyJSON.value(jsontext)
        end
    catch e
        @error "Erreur lors du chargement de $filepath" exception=e
        rethrow(e)
    end
end

"""
    get_cached_json(filepath::String) -> LazyJSON.Value

Récupère un fichier JSON depuis le cache ou le charge si nécessaire.
Gère automatiquement la limite de cache.
"""
function get_cached_json(filepath::String)::LazyJSON.Value
    # Normaliser le chemin
    normalized_path = abspath(filepath)
    
    # Vérifier le cache
    if haskey(FILE_CACHE, normalized_path)
        ACTIVE_CONFIG[].cache_stats && (CACHE_STATS.file_hits += 1)
        @info "Cache HIT pour fichier: $(basename(normalized_path))"
        return FILE_CACHE[normalized_path]
    end
    
    # Cache miss - charger le fichier
    ACTIVE_CONFIG[].cache_stats && (CACHE_STATS.file_misses += 1)
    @info "Cache MISS pour fichier: $(basename(normalized_path)) - Chargement..."
    
    # Gérer la limite de cache
    if length(FILE_CACHE) >= ACTIVE_CONFIG[].max_file_cache
        cleanup_file_cache()
    end
    
    # Charger et mettre en cache
    json_data = load_json_with_mmap(normalized_path)
    FILE_CACHE[normalized_path] = json_data
    
    # Mettre à jour les statistiques mémoire
    update_memory_stats()
    
    @info "Fichier chargé et mis en cache: $(basename(normalized_path))"
    return json_data
end

# ============================================================================
# FONCTIONS DE CONVERSION ET EXTRACTION
# ============================================================================

"""
    convert_lazy_main_to_struct(lazy_main) -> MainData

Convertit une main LazyJSON vers la structure MainData.
"""
function convert_lazy_main_to_struct(lazy_main)::MainData
    return MainData(
        Int(lazy_main["index1"]),           # Convertir vers Int
        String(lazy_main["index2"]),        # Convertir vers String
        String(lazy_main["index3"]),        # Convertir vers String
        String(lazy_main["index5"])         # Convertir vers String
    )
end

"""
    extract_partie_data(json_data::LazyJSON.Value, partie_id::Int) -> PartieData

Extrait les données d'une partie spécifique depuis le JSON paresseux.
Conversion optimisée avec threading si activé.
"""
function extract_partie_data(json_data::LazyJSON.Value, partie_id::Int)::PartieData
    try
        # Chercher la partie par son numero (pas par index de tableau)
        parties_condensees = json_data["parties_condensees"]
        partie_lazy = nothing

        for partie in parties_condensees
            if partie["partie_number"] == partie_id
                partie_lazy = partie
                break
            end
        end

        if partie_lazy === nothing
            throw(ArgumentError("Partie numéro $partie_id non trouvée"))
        end

        # Extraction des mains
        mains_lazy = partie_lazy["mains_condensees"]
        
        # Conversion avec ou sans threading selon config
        mains_data = if ACTIVE_CONFIG[].enable_threading && length(mains_lazy) > 50
            # Threading pour grosses parties (>50 mains)
            convert_mains_threaded(mains_lazy)
        else
            # Conversion séquentielle pour petites parties
            [convert_lazy_main_to_struct(main) for main in mains_lazy]
        end
        
        # Extraction metadata avec conversion des types LazyJSON
        metadata = Dict{String, Any}()
        for key_lazy in keys(partie_lazy)
            key_str = String(key_lazy)  # Convertir la clé LazyJSON en String
            if key_str != "mains_condensees"
                # Convertir les valeurs LazyJSON vers des types Julia standards
                value = partie_lazy[key_lazy]
                if isa(value, LazyJSON.String)
                    metadata[key_str] = String(value)
                elseif isa(value, LazyJSON.Number)
                    metadata[key_str] = Int(value)
                else
                    metadata[key_str] = value
                end
            end
        end
        
        return PartieData(partie_id, mains_data, metadata)
        
    catch e
        @error "Erreur lors de l'extraction de la partie $partie_id" exception=e
        rethrow(e)
    end
end

"""
    convert_mains_threaded(mains_lazy) -> Vector{MainData}

Conversion des mains avec threading pour optimiser sur 8 cœurs.
"""
function convert_mains_threaded(mains_lazy)::Vector{MainData}
    n_mains = length(mains_lazy)
    mains_data = Vector{MainData}(undef, n_mains)

    # Parallélisation sur les 8 cœurs disponibles
    @threads for i in 1:n_mains
        mains_data[i] = convert_lazy_main_to_struct(mains_lazy[i])
    end

    return mains_data
end

# ============================================================================
# FONCTIONS DE CACHE AVEC MEMOIZATION
# ============================================================================

"""
    get_cached_partie(filepath::String, partie_id::Int) -> PartieData

Récupère une partie depuis le cache ou l'extrait si nécessaire.
Utilise memoization pour performance optimale.
"""
@memoize function get_cached_partie(filepath::String, partie_id::Int)::PartieData
    # Clé de cache
    cache_key = (abspath(filepath), partie_id)

    # Vérifier le cache des parties
    if haskey(PARTIE_CACHE, cache_key)
        ACTIVE_CONFIG[].cache_stats && (CACHE_STATS.partie_hits += 1)
        @info "Cache HIT pour partie $partie_id"
        return PARTIE_CACHE[cache_key]
    end

    # Cache miss - extraire la partie
    ACTIVE_CONFIG[].cache_stats && (CACHE_STATS.partie_misses += 1)
    @info "Cache MISS pour partie $partie_id - Extraction..."

    # Gérer la limite de cache des parties
    if length(PARTIE_CACHE) >= ACTIVE_CONFIG[].max_parties_cache
        cleanup_partie_cache()
    end

    # Charger le JSON et extraire la partie
    json_data = get_cached_json(filepath)
    partie_data = extract_partie_data(json_data, partie_id)

    # Mettre en cache
    PARTIE_CACHE[cache_key] = partie_data

    @info "Partie $partie_id extraite et mise en cache"
    return partie_data
end

# ============================================================================
# FONCTIONS DE GESTION DU CACHE
# ============================================================================

"""
    cleanup_file_cache()

Nettoie le cache des fichiers en gardant les plus récemment utilisés.
"""
function cleanup_file_cache()
    if length(FILE_CACHE) <= ACTIVE_CONFIG[].max_file_cache
        return
    end

    @info "Nettoyage du cache des fichiers..."

    # Garder seulement la moitié des fichiers (stratégie simple)
    keep_count = ACTIVE_CONFIG[].max_file_cache ÷ 2
    files_to_keep = collect(keys(FILE_CACHE))[1:keep_count]

    # Vider le cache et garder seulement les fichiers sélectionnés
    old_cache = copy(FILE_CACHE)
    empty!(FILE_CACHE)

    for file in files_to_keep
        FILE_CACHE[file] = old_cache[file]
    end

    @info "Cache des fichiers nettoyé: $(length(old_cache)) -> $(length(FILE_CACHE)) fichiers"
end

"""
    cleanup_partie_cache()

Nettoie le cache des parties en gardant les plus récemment utilisées.
"""
function cleanup_partie_cache()
    if length(PARTIE_CACHE) <= ACTIVE_CONFIG[].max_parties_cache
        return
    end

    @info "Nettoyage du cache des parties..."

    # Garder seulement la moitié des parties (stratégie simple)
    keep_count = ACTIVE_CONFIG[].max_parties_cache ÷ 2
    parties_to_keep = collect(keys(PARTIE_CACHE))[1:keep_count]

    # Vider le cache et garder seulement les parties sélectionnées
    old_cache = copy(PARTIE_CACHE)
    empty!(PARTIE_CACHE)

    for partie_key in parties_to_keep
        PARTIE_CACHE[partie_key] = old_cache[partie_key]
    end

    @info "Cache des parties nettoyé: $(length(old_cache)) -> $(length(PARTIE_CACHE)) parties"
end

"""
    clear_all_caches()

Vide complètement tous les caches.
"""
function clear_all_caches()
    @info "Vidage complet de tous les caches..."

    empty!(FILE_CACHE)
    empty!(PARTIE_CACHE)
    empty!(memoize_cache(get_cached_partie))

    # Reset des statistiques
    CACHE_STATS.file_hits = 0
    CACHE_STATS.file_misses = 0
    CACHE_STATS.partie_hits = 0
    CACHE_STATS.partie_misses = 0
    CACHE_STATS.total_memory_mb = 0.0
    CACHE_STATS.last_cleanup = now()

    @info "Tous les caches ont été vidés"
end

"""
    update_memory_stats()

Met à jour les statistiques d'utilisation mémoire.
"""
function update_memory_stats()
    if ACTIVE_CONFIG[].cache_stats
        # Estimation approximative de l'utilisation mémoire
        file_cache_size = length(FILE_CACHE) * 50  # ~50MB par fichier JSON estimé
        partie_cache_size = length(PARTIE_CACHE) * 5  # ~5MB par partie estimée

        CACHE_STATS.total_memory_mb = file_cache_size + partie_cache_size
    end
end

# ============================================================================
# FONCTIONS D'INTERFACE PUBLIQUE
# ============================================================================

"""
    load_dataset_cached(filepath::String = "C:\\Users\\<USER>\\Desktop\\modules\\partie\\dataset.json") -> LazyJSON.Value

Charge le dataset principal avec mise en cache optimisée.
Chemin par défaut vers le fichier dataset.json du projet.
"""
function load_dataset_cached(filepath::String = "C:\\Users\\<USER>\\Desktop\\modules\\partie\\dataset.json")::LazyJSON.Value
    @info "Chargement du dataset: $(basename(filepath))"
    return get_cached_json(filepath)
end

"""
    get_partie_cached(partie_id::Int, filepath::String = "C:\\Users\\<USER>\\Desktop\\modules\\partie\\dataset.json") -> PartieData

Récupère une partie spécifique avec mise en cache optimisée.
"""
function get_partie_cached(partie_id::Int, filepath::String = "C:\\Users\\<USER>\\Desktop\\modules\\partie\\dataset.json")::PartieData
    @info "Récupération de la partie $partie_id"
    return get_cached_partie(filepath, partie_id)
end

"""
    get_mains_cached(partie_id::Int, filepath::String = "C:\\Users\\<USER>\\Desktop\\modules\\partie\\dataset.json") -> Vector{MainData}

Récupère les mains d'une partie avec mise en cache optimisée.
"""
function get_mains_cached(partie_id::Int, filepath::String = "C:\\Users\\<USER>\\Desktop\\modules\\partie\\dataset.json")::Vector{MainData}
    partie = get_partie_cached(partie_id, filepath)
    return partie.mains
end

"""
    count_parties_in_dataset(filepath::String = "C:\\Users\\<USER>\\Desktop\\modules\\partie\\dataset.json") -> Int

Compte le nombre de parties dans le dataset.
"""
function count_parties_in_dataset(filepath::String = "C:\\Users\\<USER>\\Desktop\\modules\\partie\\dataset.json")::Int
    json_data = get_cached_json(filepath)
    return length(json_data["parties_condensees"])
end

"""
    get_partie_info(partie_id::Int, filepath::String = "C:\\Users\\<USER>\\Desktop\\modules\\partie\\dataset.json") -> Dict{String, Any}

Récupère les informations d'une partie sans charger toutes les mains.
"""
function get_partie_info(partie_id::Int, filepath::String = "C:\\Users\\<USER>\\Desktop\\modules\\partie\\dataset.json")::Dict{String, Any}
    json_data = get_cached_json(filepath)
    partie_lazy = json_data["parties_condensees"][partie_id]

    info = Dict{String, Any}()
    for key_lazy in keys(partie_lazy)
        key_str = String(key_lazy)  # Convertir la clé LazyJSON en String
        if key_str != "mains_condensees"
            # Convertir les valeurs LazyJSON vers des types Julia standards
            value = partie_lazy[key_lazy]
            if isa(value, LazyJSON.String)
                info[key_str] = String(value)
            elseif isa(value, LazyJSON.Number)
                info[key_str] = Int(value)
            else
                info[key_str] = value
            end
        else
            info["nb_mains"] = length(partie_lazy["mains_condensees"])
        end
    end

    return info
end

# ============================================================================
# FONCTIONS DE CONFIGURATION ET STATISTIQUES
# ============================================================================

"""
    configure_cache(;max_parties::Int = 100, max_files::Int = 5, threading::Bool = true, stats::Bool = true)

Configure les paramètres du cache.
"""
function configure_cache(;max_parties::Int = 100, max_files::Int = 5, threading::Bool = true, stats::Bool = true)
    new_config = CacheConfig(max_parties, max_files, threading, stats)
    ACTIVE_CONFIG[] = new_config

    @info "Configuration du cache mise à jour:" new_config

    # Nettoyer les caches si les nouvelles limites sont plus basses
    if length(FILE_CACHE) > max_files
        cleanup_file_cache()
    end
    if length(PARTIE_CACHE) > max_parties
        cleanup_partie_cache()
    end
end

"""
    get_cache_stats() -> NamedTuple

Retourne les statistiques détaillées du cache.
"""
function get_cache_stats()
    update_memory_stats()

    return (
        file_cache_size = length(FILE_CACHE),
        partie_cache_size = length(PARTIE_CACHE),
        file_hits = CACHE_STATS.file_hits,
        file_misses = CACHE_STATS.file_misses,
        partie_hits = CACHE_STATS.partie_hits,
        partie_misses = CACHE_STATS.partie_misses,
        file_hit_rate = CACHE_STATS.file_hits / max(1, CACHE_STATS.file_hits + CACHE_STATS.file_misses),
        partie_hit_rate = CACHE_STATS.partie_hits / max(1, CACHE_STATS.partie_hits + CACHE_STATS.partie_misses),
        estimated_memory_mb = CACHE_STATS.total_memory_mb,
        last_cleanup = CACHE_STATS.last_cleanup,
        threads_available = Threads.nthreads(),
        threading_enabled = ACTIVE_CONFIG[].enable_threading
    )
end

"""
    print_cache_stats()

Affiche les statistiques du cache de manière formatée.
"""
function print_cache_stats()
    stats = get_cache_stats()

    println("=" ^ 60)
    println("STATISTIQUES DU CACHE JSON")
    println("=" ^ 60)
    println("Fichiers en cache: $(stats.file_cache_size)")
    println("Parties en cache: $(stats.partie_cache_size)")
    println()
    println("Hits fichiers: $(stats.file_hits) ($(round(stats.file_hit_rate * 100, digits=1))%)")
    println("Misses fichiers: $(stats.file_misses)")
    println("Hits parties: $(stats.partie_hits) ($(round(stats.partie_hit_rate * 100, digits=1))%)")
    println("Misses parties: $(stats.partie_misses)")
    println()
    println("Mémoire estimée: $(round(stats.estimated_memory_mb, digits=1)) MB")
    println("Threads disponibles: $(stats.threads_available)")
    println("Threading activé: $(stats.threading_enabled)")
    println("Dernier nettoyage: $(stats.last_cleanup)")
    println("=" ^ 60)
end

# ============================================================================
# FONCTIONS DE TEST ET BENCHMARK
# ============================================================================

"""
    benchmark_cache_performance(partie_ids::Vector{Int} = [1, 2, 3, 4, 5])

Benchmark des performances du cache avec différentes parties.
"""
function benchmark_cache_performance(partie_ids::Vector{Int} = [1, 2, 3, 4, 5])
    @info "Début du benchmark de performance du cache"

    # Vider le cache pour test propre
    clear_all_caches()

    # Premier passage - cache froid
    @info "Test avec cache froid..."
    cold_times = Float64[]
    for partie_id in partie_ids
        time_taken = @elapsed get_partie_cached(partie_id)
        push!(cold_times, time_taken)
        @info "Partie $partie_id (cache froid): $(round(time_taken * 1000, digits=2)) ms"
    end

    # Deuxième passage - cache chaud
    @info "Test avec cache chaud..."
    hot_times = Float64[]
    for partie_id in partie_ids
        time_taken = @elapsed get_partie_cached(partie_id)
        push!(hot_times, time_taken)
        @info "Partie $partie_id (cache chaud): $(round(time_taken * 1000, digits=2)) ms"
    end

    # Statistiques
    avg_cold = mean(cold_times) * 1000
    avg_hot = mean(hot_times) * 1000
    speedup = avg_cold / avg_hot

    println("\n" * "=" ^ 50)
    println("RÉSULTATS DU BENCHMARK")
    println("=" ^ 50)
    println("Temps moyen cache froid: $(round(avg_cold, digits=2)) ms")
    println("Temps moyen cache chaud: $(round(avg_hot, digits=2)) ms")
    println("Accélération: $(round(speedup, digits=1))x")
    println("=" ^ 50)

    print_cache_stats()

    return (cold_times = cold_times, hot_times = hot_times, speedup = speedup)
end

# ============================================================================
# EXPORTS
# ============================================================================

export MainData, PartieData, CacheConfig
export load_dataset_cached, get_partie_cached, get_mains_cached
export count_parties_in_dataset, get_partie_info
export configure_cache, get_cache_stats, print_cache_stats
export clear_all_caches, benchmark_cache_performance

end # module CacheJSON
