include("CacheJSON.jl")
using .CacheJSON

println("=== TEST DEBUG OPTIMISATIONS ===")

try
    # Test simple
    println("1. Test chargement JSON...")
    json_data = CacheJSON.get_cached_json("C:\\Users\\<USER>\\Desktop\\modules\\partie\\dataset.json")
    println("   ✅ JSON chargé")
    
    println("2. Test construction index...")
    index_map = CacheJSON.build_partie_index(json_data, "test")
    println("   ✅ Index construit: $(length(index_map)) parties")
    
    println("3. Test extraction partie 1...")
    partie1 = CacheJSON.get_partie_cached(1)
    println("   ✅ Partie 1: $(length(partie1.mains)) mains")
    
    println("4. Test extraction batch...")
    parties = CacheJSON.get_parties_batch_ultra_fast([1, 2])
    println("   ✅ Batch: $(length(parties)) parties")
    
    println("✅ Tous les tests passent!")
    
catch e
    println("❌ Erreur: $e")
    println("Stacktrace:")
    for (i, frame) in enumerate(stacktrace(catch_backtrace()))
        println("  $i. $frame")
        if i > 5  # Limiter l'affichage
            break
        end
    end
end
