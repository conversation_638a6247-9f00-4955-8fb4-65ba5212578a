include("CacheJSON.jl")
using .CacheJSON

println("=== BENCHMARK FICHIER 678 MB - OBJECTIF 500 MB/s ===")

# Configuration ultra-performance pour gros fichier
CacheJSON.configure_cache(
    max_parties = 1000,
    max_files = 1, 
    threading = true,
    stats = false
)

# Informations fichier
filepath = "C:\\Users\\<USER>\\Desktop\\modules\\partie\\dataset.json"
file_size_bytes = 711276114
file_size_mb = file_size_bytes / (1024 * 1024)
println("📁 Fichier: dataset.json")
println("📊 Taille: $(round(file_size_mb, digits=1)) MB")
println("🎯 Objectif: 500 MB/s")
println("="^60)

# Test 1: Chargement initial complet (cache froid)
println("\n🔥 TEST 1: CHARGEMENT INITIAL (cache froid)")
println("Chargement du fichier de $(round(file_size_mb, digits=1)) MB...")

# Vider les caches pour test propre
CacheJSON.clear_all_caches()

start_time = time()
json_data = CacheJSON.get_cached_json(filepath)
end_time = time()

load_time = end_time - start_time
speed_mbs = file_size_mb / load_time

println("⏱️  Temps total: $(round(load_time, digits=3)) secondes")
println("🚀 Vitesse: $(round(speed_mbs, digits=1)) MB/s")

if speed_mbs >= 500
    println("✅ OBJECTIF 500 MB/s ATTEINT!")
elseif speed_mbs >= 250
    println("✅ Très bonnes performances (>250 MB/s)")
else
    println("⚠️  Performances à améliorer")
end

# Test 2: Accès cache chaud
println("\n⚡ TEST 2: ACCÈS CACHE CHAUD")
start_time = time()
json_data_cached = CacheJSON.get_cached_json(filepath)
end_time = time()

cached_time = end_time - start_time
if cached_time > 0
    cached_speed = file_size_mb / cached_time
    println("⏱️  Temps: $(round(cached_time * 1000, digits=3)) ms")
    println("🚀 Vitesse: $(round(cached_speed, digits=1)) MB/s")
else
    println("⏱️  Temps: < 1 ms (instantané)")
    println("🚀 Vitesse: ∞ MB/s (cache parfait)")
end

# Test 3: Comptage parties sur gros fichier
println("\n📊 TEST 3: COMPTAGE PARTIES")
start_time = time()
nb_parties = CacheJSON.count_parties_in_dataset(filepath)
end_time = time()

count_time = end_time - start_time
println("📈 Parties trouvées: $nb_parties")
println("⏱️  Temps comptage: $(round(count_time * 1000, digits=3)) ms")

# Test 4: Extraction parties en série
println("\n🔄 TEST 4: EXTRACTION PARTIES EN SÉRIE")
start_time = time()
for i in 1:5
    partie = CacheJSON.get_partie_cached(i)
end
end_time = time()

extract_time = end_time - start_time
parties_per_sec = 5 / extract_time
println("⏱️  5 parties en: $(round(extract_time, digits=3)) secondes")
println("🚀 Vitesse: $(round(parties_per_sec, digits=1)) parties/seconde")

# Résumé final
println("\n" * "="^60)
println("📋 RÉSUMÉ FINAL - FICHIER 678 MB")
println("="^60)
println("🎯 Objectif: 500 MB/s")
println("📊 Chargement initial: $(round(speed_mbs, digits=1)) MB/s")
println("⚡ Cache chaud: Instantané")
println("📈 Parties disponibles: $nb_parties")
println("🔄 Extraction: $(round(parties_per_sec, digits=1)) parties/sec")

if speed_mbs >= 500
    println("🏆 EXCELLENT: Objectif 500 MB/s atteint!")
elseif speed_mbs >= 250
    println("✅ TRÈS BON: Performance >250 MB/s")
else
    println("⚠️  À améliorer: Performance <250 MB/s")
end
println("="^60)
