"""
test_cache.jl - Script de test et démonstration du système de cache JSON
Teste les performances et fonctionnalités du module CacheJSON.jl

Usage:
    julia test_cache.jl
"""

# Inclure le module de cache
include("CacheJSON.jl")
using .CacheJSON
using Dates

# ============================================================================
# FONCTIONS DE TEST
# ============================================================================

"""
    test_basic_functionality()

Test des fonctionnalités de base du cache.
"""
function test_basic_functionality()
    println("\n🧪 TEST DES FONCTIONNALITÉS DE BASE")
    println("=" ^ 50)
    
    try
        # Test 1: Chargement du dataset
        println("1. Test de chargement du dataset...")
        dataset = load_dataset_cached()
        println("   ✅ Dataset chargé avec succès")
        
        # Test 2: Comptage des parties
        println("2. Test de comptage des parties...")
        nb_parties = count_parties_in_dataset()
        println("   ✅ Nombre de parties trouvées: $nb_parties")
        
        # Test 3: Récupération d'une partie
        println("3. Test de récupération d'une partie...")
        if nb_parties > 0
            partie = get_partie_cached(1)
            println("   ✅ Partie 1 récupérée: $(length(partie.mains)) mains")
        else
            println("   ⚠️  Aucune partie disponible pour le test")
        end
        
        # Test 4: Informations d'une partie
        println("4. Test d'informations d'une partie...")
        if nb_parties > 0
            info = get_partie_info(1)
            println("   ✅ Informations partie 1: $(length(info)) champs")
        end
        
        println("   ✅ Tous les tests de base réussis!")
        
    catch e
        println("   ❌ Erreur lors des tests de base: $e")
        return false
    end
    
    return true
end

"""
    test_cache_performance()

Test des performances du cache avec différents scénarios.
"""
function test_cache_performance()
    println("\n⚡ TEST DES PERFORMANCES DU CACHE")
    println("=" ^ 50)
    
    try
        # Configuration pour test
        configure_cache(max_parties=10, max_files=2, threading=true, stats=true)
        
        # Vider le cache pour test propre
        clear_all_caches()
        
        # Test avec plusieurs parties
        parties_test = [1, 2, 3, 1, 2, 3]  # Répétition pour tester le cache
        
        println("Test de performance avec parties: $parties_test")
        
        for (i, partie_id) in enumerate(parties_test)
            print("Partie $partie_id (essai $i): ")
            
            time_taken = @elapsed begin
                partie = get_partie_cached(partie_id)
                nb_mains = length(partie.mains)
            end
            
            println("$(round(time_taken * 1000, digits=2)) ms ($(nb_mains) mains)")
        end
        
        # Afficher les statistiques
        print_cache_stats()
        
        println("   ✅ Test de performance terminé!")
        
    catch e
        println("   ❌ Erreur lors du test de performance: $e")
        return false
    end
    
    return true
end

"""
    test_threading_performance()

Test de l'impact du threading sur les performances.
"""
function test_threading_performance()
    println("\n🧵 TEST DES PERFORMANCES THREADING")
    println("=" ^ 50)
    
    try
        # Test sans threading
        println("Test SANS threading...")
        configure_cache(threading=false)
        clear_all_caches()
        
        time_no_thread = @elapsed begin
            for i in 1:3
                partie = get_partie_cached(i)
            end
        end
        
        println("Temps sans threading: $(round(time_no_thread * 1000, digits=2)) ms")
        
        # Test avec threading
        println("Test AVEC threading...")
        configure_cache(threading=true)
        clear_all_caches()
        
        time_with_thread = @elapsed begin
            for i in 1:3
                partie = get_partie_cached(i)
            end
        end
        
        println("Temps avec threading: $(round(time_with_thread * 1000, digits=2)) ms")
        
        # Comparaison
        if time_no_thread > 0
            speedup = time_no_thread / time_with_thread
            println("Accélération avec threading: $(round(speedup, digits=2))x")
        end
        
        println("   ✅ Test de threading terminé!")
        
    catch e
        println("   ❌ Erreur lors du test de threading: $e")
        return false
    end
    
    return true
end

"""
    test_memory_management()

Test de la gestion mémoire et du nettoyage du cache.
"""
function test_memory_management()
    println("\n💾 TEST DE GESTION MÉMOIRE")
    println("=" ^ 50)
    
    try
        # Configuration avec limites basses pour forcer le nettoyage
        configure_cache(max_parties=3, max_files=1)
        clear_all_caches()
        
        println("Configuration: max 3 parties, max 1 fichier")
        
        # Charger plus de parties que la limite
        println("Chargement de 5 parties (> limite de 3)...")
        for i in 1:5
            partie = get_partie_cached(i)
            stats = get_cache_stats()
            println("Partie $i chargée - Cache: $(stats.partie_cache_size) parties")
        end
        
        # Vérifier que le cache a été nettoyé
        final_stats = get_cache_stats()
        if final_stats.partie_cache_size <= 3
            println("   ✅ Nettoyage automatique du cache fonctionnel!")
        else
            println("   ⚠️  Nettoyage du cache non effectué")
        end
        
        # Test de vidage complet
        println("Test de vidage complet du cache...")
        clear_all_caches()
        empty_stats = get_cache_stats()
        
        if empty_stats.partie_cache_size == 0 && empty_stats.file_cache_size == 0
            println("   ✅ Vidage complet réussi!")
        else
            println("   ⚠️  Vidage complet incomplet")
        end
        
        println("   ✅ Test de gestion mémoire terminé!")
        
    catch e
        println("   ❌ Erreur lors du test de gestion mémoire: $e")
        return false
    end
    
    return true
end

"""
    run_full_benchmark()

Lance un benchmark complet du système de cache.
"""
function run_full_benchmark()
    println("\n🏁 BENCHMARK COMPLET DU SYSTÈME")
    println("=" ^ 50)
    
    try
        # Restaurer configuration optimale
        configure_cache(max_parties=100, max_files=5, threading=true, stats=true)
        
        # Benchmark avec les 5 premières parties
        results = benchmark_cache_performance([1, 2, 3, 4, 5])
        
        println("   ✅ Benchmark complet terminé!")
        return results
        
    catch e
        println("   ❌ Erreur lors du benchmark complet: $e")
        return nothing
    end
end

# ============================================================================
# FONCTION PRINCIPALE
# ============================================================================

"""
    main()

Fonction principale qui lance tous les tests.
"""
function main()
    println("🚀 DÉMARRAGE DES TESTS DU SYSTÈME DE CACHE JSON")
    println("Optimisé pour 8 cœurs CPU et 28GB RAM")
    println("Fichier cible: C:\\Users\\<USER>\\Desktop\\modules\\partie\\dataset.json")
    println("Heure de début: $(Dates.format(now(), "yyyy-mm-dd HH:MM:SS"))")
    
    # Vérifier que le fichier existe
    dataset_path = "C:\\Users\\<USER>\\Desktop\\modules\\partie\\dataset.json"
    if !isfile(dataset_path)
        println("❌ ERREUR: Fichier dataset.json non trouvé!")
        println("Chemin: $dataset_path")
        return
    end
    
    println("✅ Fichier dataset.json trouvé")
    
    # Lancer tous les tests
    tests_passed = 0
    total_tests = 5
    
    # Test 1: Fonctionnalités de base
    if test_basic_functionality()
        tests_passed += 1
    end
    
    # Test 2: Performances du cache
    if test_cache_performance()
        tests_passed += 1
    end
    
    # Test 3: Threading
    if test_threading_performance()
        tests_passed += 1
    end
    
    # Test 4: Gestion mémoire
    if test_memory_management()
        tests_passed += 1
    end
    
    # Test 5: Benchmark complet
    if run_full_benchmark() !== nothing
        tests_passed += 1
    end
    
    # Résumé final
    println("\n" * "=" ^ 60)
    println("RÉSUMÉ FINAL DES TESTS")
    println("=" ^ 60)
    println("Tests réussis: $tests_passed/$total_tests")
    println("Heure de fin: $(Dates.format(now(), "yyyy-mm-dd HH:MM:SS"))")
    
    if tests_passed == total_tests
        println("🎉 TOUS LES TESTS SONT PASSÉS AVEC SUCCÈS!")
        println("Le système de cache JSON est opérationnel.")
    else
        println("⚠️  Certains tests ont échoué. Vérifiez les erreurs ci-dessus.")
    end
    
    # Statistiques finales
    print_cache_stats()
end

# Lancer les tests si le script est exécuté directement
if abspath(PROGRAM_FILE) == @__FILE__
    main()
end
