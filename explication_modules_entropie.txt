EXPLICATION COMPLÈTE DES 8 MODULES D'ENTROPIE
===========================================
Analyse en langage naturel de chaque module Julia
Date : 13 juillet 2025

═══════════════════════════════════════════════════════════════════════════════
MODULE 1 : ShannonT.jl - ENTROPIE DE SHANNON THÉORIQUE (PRIORITÉ 5)
═══════════════════════════════════════════════════════════════════════════════

QUE CALCULE CE MODULE :
ShannonT calcule l'entropie de Shannon "pure" en utilisant les probabilités théoriques INDEX5.

PRINCIPE :
- Regarde quelles valeurs INDEX5 différentes apparaissent dans la partie [main 1 à main n]
- Pour chaque valeur unique observée, utilise sa probabilité théorique INDEX5
- Applique la formule classique de Shannon : H = -∑ p_théo(x) × log₂(p_théo(x))

EXEMPLE CONCRET :
Si dans les 5 premières mains on observe : ["0_A_BANKER", "1_B_PLAYER", "0_A_BANKER"]
- Valeurs uniques : "0_A_BANKER" et "1_B_PLAYER"
- ShannonT = -p_théo("0_A_BANKER") × log₂(p_théo("0_A_BANKER")) 
             -p_théo("1_B_PLAYER") × log₂(p_théo("1_B_PLAYER"))

INTERPRÉTATION :
- ShannonT élevé : Les valeurs observées ont des probabilités théoriques faibles (surprenantes)
- ShannonT faible : Les valeurs observées ont des probabilités théoriques élevées (attendues)

USAGE : Métrique de base pour mesurer la "surprise" théorique des valeurs observées.

═══════════════════════════════════════════════════════════════════════════════
MODULE 2 : CondT.jl - ENTROPIE CONDITIONNELLE MOYENNE (PRIORITÉ 1)
═══════════════════════════════════════════════════════════════════════════════

QUE CALCULE CE MODULE :
CondT calcule l'entropie conditionnelle moyenne - la métrique LA PLUS IMPORTANTE pour la prédiction.

PRINCIPE :
- Pour chaque main i de 1 à n, calcule combien d'information elle apporte sachant toutes les mains précédentes
- Main 1 : H(X₁) = information de la première main
- Main 2 : H(X₂|X₁) = information de la main 2 sachant la main 1
- Main 3 : H(X₃|X₁,X₂) = information de la main 3 sachant les mains 1 et 2
- Fait la moyenne de toutes ces entropies conditionnelles

FORMULE : CondT = (1/n) × [H(X₁) + H(X₂|X₁) + H(X₃|X₁,X₂) + ... + H(Xₙ|X₁,...,Xₙ₋₁)]

EXEMPLE CONCRET :
Pour 3 mains ["0_A_BANKER", "1_B_PLAYER", "0_C_TIE"] :
- H(X₁) = surprise de "0_A_BANKER"
- H(X₂|X₁) = surprise de "1_B_PLAYER" sachant qu'avant il y avait "0_A_BANKER"
- H(X₃|X₁,X₂) = surprise de "0_C_TIE" sachant l'historique complet
- CondT = moyenne de ces 3 valeurs

INTERPRÉTATION :
- CondT faible : Le système a beaucoup d'ordre/structure (chaque nouvelle main apporte peu d'information)
- CondT élevé : Le système est désordonné/chaotique (chaque nouvelle main apporte beaucoup d'information)

USAGE : Indicateur principal pour savoir si le système a assez de structure pour être analysé.

═══════════════════════════════════════════════════════════════════════════════
MODULE 3 : DivKLT.jl - DIVERGENCE KULLBACK-LEIBLER (PRIORITÉ 2)
═══════════════════════════════════════════════════════════════════════════════

QUE CALCULE CE MODULE :
DivKLT mesure l'écart entre ce qui est réellement observé dans la partie et ce que prédit le modèle INDEX5.

PRINCIPE :
- Calcule les fréquences réelles de chaque valeur INDEX5 dans la partie [main 1 à main n]
- Compare ces fréquences aux probabilités théoriques INDEX5
- Utilise la divergence KL : ∑ p_observé(x) × log₂(p_observé(x)/p_théorique(x))

EXEMPLE CONCRET :
Si "0_A_BANKER" apparaît 3 fois sur 10 mains :
- Fréquence observée : 3/10 = 0.3
- Probabilité théorique INDEX5 : 0.085136
- Contribution à DivKL : 0.3 × log₂(0.3/0.085136)

INTERPRÉTATION :
- DivKLT = 0 : Les observations correspondent parfaitement au modèle INDEX5
- DivKLT > 0 : Les observations s'écartent du modèle INDEX5
- Plus DivKLT est élevé, plus l'écart est important

USAGE : Validation du modèle INDEX5 - permet de savoir si le modèle théorique est fiable.

═══════════════════════════════════════════════════════════════════════════════
MODULE 4 : CrossT.jl - ENTROPIE CROISÉE (PRIORITÉ 2)
═══════════════════════════════════════════════════════════════════════════════

QUE CALCULE CE MODULE :
CrossT mesure le "coût d'encodage" des données réelles en utilisant le modèle INDEX5 comme système de codage.

PRINCIPE :
- Calcule les fréquences réelles de chaque valeur dans la partie
- Pour chaque valeur, utilise sa probabilité théorique INDEX5 pour calculer le coût d'encodage
- Formule : -∑ p_observé(x) × log₂(p_théorique(x))

EXEMPLE CONCRET :
Si "0_A_BANKER" apparaît 3 fois sur 10 mains :
- Fréquence observée : 0.3
- Probabilité théorique INDEX5 : 0.085136
- Contribution : -0.3 × log₂(0.085136)

INTERPRÉTATION :
- CrossT faible : Le modèle INDEX5 encode efficacement les données observées
- CrossT élevé : Le modèle INDEX5 est inefficace pour encoder les données observées

USAGE : Évaluation de l'efficacité du modèle INDEX5 comme système de prédiction/compression.

═══════════════════════════════════════════════════════════════════════════════
MODULE 5 : MetricT.jl - ENTROPIE MÉTRIQUE PONDÉRÉE (PRIORITÉ 3)
═══════════════════════════════════════════════════════════════════════════════

QUE CALCULE CE MODULE :
MetricT mesure l'impact de l'ajout d'une nouvelle main sur la complexité informationnelle globale du système.

PRINCIPE :
- Calcule une "complexité pondérée" pour la séquence [main 1 à main n-1]
- Calcule une "complexité pondérée" pour la séquence [main 1 à main n]
- Fait la différence : MetricT = Complexité(n) - Complexité(n-1)
- La pondération donne plus d'importance aux mains récentes

FORMULE DE PONDÉRATION :
Chaque main i contribue avec un poids i (les mains récentes comptent plus)
Complexité = (2/(k(k+1))) × ∑ᵢ₌₁ᵏ i × H(Xᵢ|X₁,...,Xᵢ₋₁)

INTERPRÉTATION :
- MetricT > 0 : La nouvelle main augmente la complexité (apporte du désordre)
- MetricT < 0 : La nouvelle main diminue la complexité (apporte de l'ordre)
- MetricT ≈ 0 : La nouvelle main n'affecte pas la complexité

USAGE : Détection de changements de régime - permet de voir si le système devient plus ou moins complexe.

═══════════════════════════════════════════════════════════════════════════════
MODULE 6 : TopoT.jl - ENTROPIE TOPOLOGIQUE MULTI-ÉCHELLES (PRIORITÉ 3)
═══════════════════════════════════════════════════════════════════════════════

QUE CALCULE CE MODULE :
TopoT analyse la complexité du système à 3 niveaux de résolution différents et fait une moyenne pondérée.

PRINCIPE :
- Échelle 1 (16.7%) : Analyse les valeurs individuelles (complexité locale)
- Échelle 2 (33.3%) : Analyse les paires consécutives (complexité des transitions)
- Échelle 3 (50.0%) : Analyse les triplets consécutifs (complexité des motifs)
- Combine les 3 échelles avec une pondération qui privilégie les motifs longs

CALCUL PAR ÉCHELLE :
Pour chaque échelle, identifie tous les blocs distincts de cette taille dans la séquence
Calcule l'entropie de ces blocs en utilisant les probabilités théoriques INDEX5

EXEMPLE CONCRET :
Séquence ["A", "B", "C", "A"] :
- Échelle 1 : blocs ["A", "B", "C"] → 3 valeurs distinctes
- Échelle 2 : blocs ["A,B", "B,C", "C,A"] → 3 paires distinctes
- Échelle 3 : blocs ["A,B,C", "B,C,A"] → 2 triplets distincts

INTERPRÉTATION :
- TopoT élevé : Structure complexe avec beaucoup de motifs différents à toutes les échelles
- TopoT faible : Structure simple avec peu de motifs distincts
- Sensible aux patterns répétitifs et aux structures hiérarchiques

USAGE : Détection de structures complexes et de patterns multi-échelles dans la séquence.

═══════════════════════════════════════════════════════════════════════════════
MODULE 7 : TauxT.jl - TAUX D'ENTROPIE (PRIORITÉ 4)
═══════════════════════════════════════════════════════════════════════════════

QUE CALCULE CE MODULE :
TauxT calcule l'entropie moyenne par sous-séquence de longueur 3 dans la partie.

PRINCIPE :
- Utilise BlockT pour calculer l'entropie totale de toutes les sous-séquences de longueur 3
- Divise par le nombre de sous-séquences : TauxT = BlockT / (n-2)
- Mesure l'entropie "par unité" de sous-séquence

EXEMPLE CONCRET :
Pour une partie de 5 mains :
- Sous-séquences de longueur 3 : [1,2,3], [2,3,4], [3,4,5] → 3 sous-séquences
- TauxT = BlockT / 3

INTERPRÉTATION :
- TauxT élevé : En moyenne, chaque sous-séquence de 3 mains est très surprenante
- TauxT faible : En moyenne, les sous-séquences de 3 mains sont prévisibles
- TauxT tend à diminuer quand la partie devient plus longue (convergence)

USAGE : Mesure de la prévisibilité locale du système sur des fenêtres de 3 mains.

═══════════════════════════════════════════════════════════════════════════════
MODULE 8 : BlockT.jl - ENTROPIE DE BLOC CUMULATIVE (PRIORITÉ 5)
═══════════════════════════════════════════════════════════════════════════════

QUE CALCULE CE MODULE :
BlockT calcule l'entropie totale de toutes les sous-séquences de longueur 3 dans la partie.

PRINCIPE :
- Découpe la séquence [main 1 à main n] en toutes les sous-séquences possibles de longueur 3
- Pour chaque sous-séquence, calcule sa probabilité jointe théorique
- Applique la formule d'entropie : -p_théo(sous-séq) × log₂(p_théo(sous-séq))
- Additionne toutes ces entropies

EXEMPLE CONCRET :
Séquence de 5 mains ["A", "B", "C", "D", "E"] :
- Sous-séquence 1 : ["A", "B", "C"] → entropie₁
- Sous-séquence 2 : ["B", "C", "D"] → entropie₂
- Sous-séquence 3 : ["C", "D", "E"] → entropie₃
- BlockT = entropie₁ + entropie₂ + entropie₃

CALCUL DES PROBABILITÉS JOINTES :
p_théo(["A", "B", "C"]) = p_théo("A") × p_théo("B") × p_théo("C")
(Hypothèse d'indépendance entre les mains)

INTERPRÉTATION :
- BlockT élevé : Les sous-séquences observées sont très surprenantes selon INDEX5
- BlockT faible : Les sous-séquences observées sont prévisibles selon INDEX5
- BlockT croît avec la longueur de la partie (accumulation d'entropie)

USAGE : Mesure de la complexité informationnelle totale accumulée dans la partie.

═══════════════════════════════════════════════════════════════════════════════
RÉSUMÉ DES PRIORITÉS ET USAGES
═══════════════════════════════════════════════════════════════════════════════

PRIORITÉ 1 - CondT : Métrique principale pour évaluer si le système a assez de structure
PRIORITÉ 2 - DivKLT & CrossT : Validation du modèle INDEX5 et efficacité d'encodage
PRIORITÉ 3 - MetricT & TopoT : Détection de changements et analyse multi-échelles
PRIORITÉ 4 - TauxT : Prévisibilité locale sur fenêtres de 3 mains
PRIORITÉ 5 - ShannonT & BlockT : Métriques de base et accumulation d'entropie

FENÊTRE D'ANALYSE : Tous les modules analysent une fenêtre CROISSANTE [main 1:main n]
PROBABILITÉS : Tous utilisent les probabilités théoriques INDEX5 comme référence
OBJECTIF : Mesurer différents aspects de l'ordre/désordre dans les parties de baccarat
