"""
    PredicteurIndex5

PRÉDICTEUR INDEX5 - VERSION RESTRUCTURÉE PROFESSIONNELLE
========================================================

Module principal pour la prédiction INDEX5 avec métriques théoriques d'entropie.
Architecture modulaire optimisée selon les bonnes pratiques Julia.

# Architecture Modulaire

- **MODULE 1**: Types et Structures de Base
- **MODULE 2**: Métriques Autonomes (Niveau 1 - Aucune dépendance)
- **MODULE 3**: Support Probabilités (Niveau 2)
- **MODULE 4**: Métriques avec Dépendances (Niveau 3)
- **MODULE 5**: Métriques Complexes (Niveau 4)
- **MODULE 6**: Orchestrateur de Calculs
- **MODULE 7**: Utilitaires et Interface

# Métriques par Niveau de Dépendance

- **NIVEAU 1** (Autonomes): DivKLT, CrossT, ShannonT, TopoT
- **NIVEAU 2** (Support): Fonctions de probabilités
- **NIVEAU 3** (Dépendantes): CondT, MetricT, BlockT
- **NIVEAU 4** (Complexes): TauxT

# Ordre de Priorité Prédictive

1. **CondT** - Entropie Conditionnelle (prévisibilité immédiate)
2. **DivKLT** - Divergence KL (biais du modèle)
3. **CrossT** - Entropie Croisée (inefficacité du modèle)
4. **MetricT** - Entropie Métrique (variation de complexité)
5. **TopoT** - Entropie Topologique (patterns multi-échelles)
6. **TauxT** - Taux d'Entropie (complexité normalisée)
7. **ShannonT** - Entropie de Shannon (diversité observée)
8. **BlockT** - Entropie Jointe (complexité totale)

# Programme Unifié

Ce programme central orchestre 8 modules de métriques d'entropie :
- **Modules Autonomes** : ShannonT, TopoT, DivKLT, CrossT
- **Modules avec Dépendances** : CondT, MetricT, TauxT, BlockT
- **Intégration Complète** : Tous les modules sont importés et coordonnés
- **Interface Unifiée** : Point d'entrée unique avec orchestration centralisée
"""
module PredicteurIndex5

# ═══════════════════════════════════════════════════════════════════════════════
# IMPORTS ET DÉPENDANCES
# ═══════════════════════════════════════════════════════════════════════════════

# Dépendances système
using JSON
using Printf
using Dates

# Système de cache JSON optimisé
include("CacheJSON.jl")
using .CacheJSON

# ═══════════════════════════════════════════════════════════════════════════════
# EXPORTS PUBLICS
# ═══════════════════════════════════════════════════════════════════════════════

# Types principaux
export MainData, FormulasTheoretical, MetriquesTheorique, DifferentielsPredictifs
export CalculateurDifferentielsPredictifs, PredictionResult, PredicteurDifferentiels

# Fonctions principales de calcul
export calculer_toutes_metriques_theoriques, calculer_differentiels_predictifs
export calculer_differentiels_pour_possibilites, predire_main_suivante

# Fonctions des modules de métriques (accès direct)
export calculer_formule5B_conditionnelle_theo          # CondT
export calculer_formule6B_divergence_kl_theo           # DivKLT
export calculer_formule8B_entropie_croisee_theo        # CrossT
export calculer_formule4B_entropie_metrique_theo       # MetricT
export calculer_formule9B_entropie_topologique_theo    # TopoT
export calculer_formule3B_taux_entropie_theo           # TauxT
export calculer_formule1B_shannon_jointe_theo          # ShannonT
export calculer_formule10B_block_cumulative_theo       # BlockT

# Fonctions utilitaires
export trouver_fichier_json_recent, charger_donnees_partie, compter_parties_disponibles
export calculer_index1_suivant, generer_valeurs_possibles

# Fonctions d'affichage et interface
export afficher_prediction, afficher_regles, exporter_prediction_vers_fichier

# Prédicteur avancé
export predire_index3, calculer_somme_differentiels_groupe, extraire_index3

# Programme principal
export main

# ═══════════════════════════════════════════════════════════════════════════════
# CONSTANTES GLOBALES
# ═══════════════════════════════════════════════════════════════════════════════

const DEFAULT_BASE = 2.0
const DEFAULT_EPSILON = 1e-12
const MAX_MAINS_TRAITEMENT = 59
const INDEX2_POSSIBLES = ["A", "B", "C"]
const INDEX3_POSSIBLES = ["BANKER", "PLAYER"]  # TIE exclu

# ═══════════════════════════════════════════════════════════════════════════════
# MODULE 1: TYPES ET STRUCTURES DE BASE
# ═══════════════════════════════════════════════════════════════════════════════

"""
    MainData

Structure immutable pour stocker les données d'une main de baccarat.

# Champs
- `main_number::Union{Int, Nothing}`: Numéro de la main
- `manche_pb_number::Union{Int, Nothing}`: Numéro de manche Player/Banker
- `index1::Union{Int, String}`: INDEX1 (0 ou 1)
- `index2::String`: INDEX2 (A, B, ou C)
- `index3::String`: INDEX3 (BANKER, PLAYER, ou TIE)
- `index5::String`: INDEX5 complet (format: INDEX1_INDEX2_INDEX3)

# Exemples
```julia
main = MainData(1, 1, 0, "A", "BANKER", "0_A_BANKER")
```
"""
struct MainData
    main_number::Union{Int, Nothing}
    manche_pb_number::Union{Int, Nothing}
    index1::Union{Int, String}
    index2::String
    index3::String
    index5::String

    # Constructeur interne avec validation
    function MainData(main_number, manche_pb_number, index1, index2, index3, index5)
        # Validation des paramètres
        if index2 ∉ INDEX2_POSSIBLES
            throw(ArgumentError("INDEX2 doit être A, B ou C, reçu: $index2"))
        end
        if index3 ∉ vcat(INDEX3_POSSIBLES, ["TIE"])
            throw(ArgumentError("INDEX3 doit être BANKER, PLAYER ou TIE, reçu: $index3"))
        end

        new(main_number, manche_pb_number, index1, index2, index3, index5)
    end
end

"""
    FormulasTheoretical{T<:AbstractFloat}

Structure mutable contenant les probabilités théoriques INDEX5 et paramètres
pour les calculs d'entropie.

# Paramètres de Type
- `T<:AbstractFloat`: Type numérique pour les calculs (Float32, Float64, etc.)

# Champs
- `base::T`: Base logarithmique pour les calculs d'entropie (défaut: 2.0)
- `epsilon::T`: Valeur epsilon pour éviter log(0) (défaut: 1e-12)
- `theoretical_probs::Dict{String,T}`: Probabilités théoriques INDEX5
- `sequence_complete::Vector{String}`: Séquence complète pour calculs empiriques

# Exemples
```julia
formulas = FormulasTheoretical{Float64}()
formulas_custom = FormulasTheoretical{Float32}(2.0f0, 1f-10)
```
"""
# Structure universelle compatible avec tous les modules de métriques
mutable struct FormulasTheoretical{T<:AbstractFloat}
    base::T
    epsilon::T
    theoretical_probs::Dict{String,T}
    sequence_complete::Vector{String}

    # Constructeur interne principal (le plus complet)
    function FormulasTheoretical{T}(
        base::T,
        epsilon::T,
        theoretical_probs::Dict{String,T},
        sequence_complete::Vector{String}
    ) where T<:AbstractFloat

        # Validation des paramètres
        if base <= 0 || base == 1
            throw(ArgumentError("La base doit être > 0 et ≠ 1, reçu: $base"))
        end
        if epsilon <= 0
            throw(ArgumentError("Epsilon doit être > 0, reçu: $epsilon"))
        end

        new{T}(base, epsilon, theoretical_probs, sequence_complete)
    end
end

# ═══════════════════════════════════════════════════════════════════════════════
# CONSTRUCTEURS UNIVERSELS COMPATIBLES AVEC TOUS LES MODULES
# ═══════════════════════════════════════════════════════════════════════════════

"""
Constructeur par défaut - Compatible avec tous les modules
"""
function FormulasTheoretical{T}(
    base::T = T(DEFAULT_BASE),
    epsilon::T = T(DEFAULT_EPSILON)
) where T<:AbstractFloat

    # Probabilités théoriques INDEX5 standard
    theoretical_probs = Dict{String,T}(
        "0_A_BANKER" => T(0.085136), "1_A_BANKER" => T(0.086389),
        "0_B_BANKER" => T(0.064676), "1_B_BANKER" => T(0.065479),
        "0_C_BANKER" => T(0.077903), "1_C_BANKER" => T(0.078929),
        "0_A_PLAYER" => T(0.085240), "1_A_PLAYER" => T(0.086361),
        "0_B_PLAYER" => T(0.076907), "1_B_PLAYER" => T(0.077888),
        "0_C_PLAYER" => T(0.059617), "1_C_PLAYER" => T(0.060352),
        "0_A_TIE" => T(0.017719), "1_A_TIE" => T(0.017978),
        "0_B_TIE" => T(0.016281), "1_B_TIE" => T(0.016482),
        "0_C_TIE" => T(0.013241), "1_C_TIE" => T(0.013423)
    )

    return FormulasTheoretical{T}(base, epsilon, theoretical_probs, String[])
end

"""
Constructeur avec séquence - Compatible avec TauxT.jl et BlockT.jl
"""
function FormulasTheoretical{T}(
    base::T,
    epsilon::T,
    sequence_complete::Vector{String}
) where T<:AbstractFloat

    # Probabilités théoriques INDEX5 standard
    theoretical_probs = Dict{String,T}(
        "0_A_BANKER" => T(0.085136), "1_A_BANKER" => T(0.086389),
        "0_B_BANKER" => T(0.064676), "1_B_BANKER" => T(0.065479),
        "0_C_BANKER" => T(0.077903), "1_C_BANKER" => T(0.078929),
        "0_A_PLAYER" => T(0.085240), "1_A_PLAYER" => T(0.086361),
        "0_B_PLAYER" => T(0.076907), "1_B_PLAYER" => T(0.077888),
        "0_C_PLAYER" => T(0.059617), "1_C_PLAYER" => T(0.060352),
        "0_A_TIE" => T(0.017719), "1_A_TIE" => T(0.017978),
        "0_B_TIE" => T(0.016281), "1_B_TIE" => T(0.016482),
        "0_C_TIE" => T(0.013241), "1_C_TIE" => T(0.013423)
    )

    return FormulasTheoretical{T}(base, epsilon, theoretical_probs, sequence_complete)
end

# Constructeurs de convenance pour tous types numériques
FormulasTheoretical(base::Real = DEFAULT_BASE, epsilon::Real = DEFAULT_EPSILON) =
    FormulasTheoretical{Float64}(Float64(base), Float64(epsilon))

FormulasTheoretical(base::Real, epsilon::Real, sequence_complete::Vector{String}) =
    FormulasTheoretical{Float64}(Float64(base), Float64(epsilon), sequence_complete)

# ═══════════════════════════════════════════════════════════════════════════════
# INCLUSION DES MODULES APRÈS DÉFINITION DE FormulasTheoretical
# ═══════════════════════════════════════════════════════════════════════════════

# Inclusion des modules de métriques d'entropie (ordre de priorité prédictive)
include("CondT.jl")      # PRIORITÉ 1 - Entropie Conditionnelle
include("DivKLT.jl")     # PRIORITÉ 2 - Divergence Kullback-Leibler
include("CrossT.jl")     # PRIORITÉ 2 - Entropie Croisée
include("MetricT.jl")    # PRIORITÉ 3 - Entropie Métrique
include("TopoT.jl")      # PRIORITÉ 3 - Entropie Topologique
include("TauxT.jl")      # PRIORITÉ 4 - Taux d'Entropie
include("ShannonT.jl")   # PRIORITÉ 5 - Entropie de Shannon
include("BlockT.jl")     # PRIORITÉ 5 - Entropie de Bloc

# Import des modules après inclusion
using .CondT, .DivKLT, .CrossT, .MetricT, .TopoT, .TauxT, .ShannonT, .BlockT

# ═══════════════════════════════════════════════════════════════════════════════
# FONCTIONS DE CONVERSION ET COMPATIBILITÉ
# ═══════════════════════════════════════════════════════════════════════════════

"""
    convert_to_universal(formulas_module) -> FormulasTheoretical

Convertit une structure FormulasTheoretical d'un module vers la version universelle.
Permet l'interopérabilité entre tous les modules.
"""
function convert_to_universal(formulas_module)
    # Extraire les champs communs
    base = formulas_module.base
    epsilon = formulas_module.epsilon
    theoretical_probs = formulas_module.theoretical_probs

    # Gérer le champ sequence_complete s'il existe
    sequence_complete = if hasfield(typeof(formulas_module), :sequence_complete)
        formulas_module.sequence_complete
    else
        String[]
    end

    # Créer la version universelle
    return FormulasTheoretical{typeof(base)}(base, epsilon, theoretical_probs, sequence_complete)
end

"""
    ensure_sequence_complete!(formulas::FormulasTheoretical, sequence::Vector{String})

S'assure que le champ sequence_complete est rempli pour les calculs qui en ont besoin.
"""
function ensure_sequence_complete!(formulas::FormulasTheoretical, sequence::Vector{String})
    if isempty(formulas.sequence_complete)
        formulas.sequence_complete = copy(sequence)
    end
    return formulas
end

"""
    MetriquesTheorique{T<:AbstractFloat}

Structure immutable pour stocker les 8 métriques théoriques calculées.

# Ordre de Priorité Prédictive (du plus important au moins important)
1. **CondT** - Entropie Conditionnelle (prévisibilité immédiate)
2. **DivKLT** - Divergence KL (biais du modèle)
3. **CrossT** - Entropie Croisée (inefficacité du modèle)
4. **MetricT** - Entropie Métrique (variation de complexité)
5. **TopoT** - Entropie Topologique (patterns multi-échelles)
6. **TauxT** - Taux d'Entropie (complexité normalisée)
7. **ShannonT** - Entropie de Shannon (diversité observée)
8. **BlockT** - Entropie Jointe (complexité totale)

# Paramètres de Type
- `T<:AbstractFloat`: Type numérique pour les métriques

# Champs
- `cond_t::T`: Entropie conditionnelle (PRIORITÉ 1)
- `divkl_t::T`: Divergence Kullback-Leibler (PRIORITÉ 2)
- `cross_t::T`: Entropie croisée (PRIORITÉ 2)
- `metric_t::T`: Entropie métrique (PRIORITÉ 3)
- `topo_t::T`: Entropie topologique (PRIORITÉ 3)
- `taux_t::T`: Taux d'entropie (PRIORITÉ 4)
- `shannon_t::T`: Entropie de Shannon (PRIORITÉ 5)
- `block_t::T`: Entropie de bloc (PRIORITÉ 5)

# Exemples
```julia
metriques = MetriquesTheorique{Float64}(1.2, 0.8, 1.5, 0.9, 1.1, 0.7, 1.3, 1.0)
```
"""
struct MetriquesTheorique{T<:AbstractFloat}
    cond_t::T         # 1. CondT - PRIORITÉ 1
    divkl_t::T        # 2. DivKLT - PRIORITÉ 2
    cross_t::T        # 3. CrossT - PRIORITÉ 2
    metric_t::T       # 4. MetricT - PRIORITÉ 3
    topo_t::T         # 5. TopoT - PRIORITÉ 3
    taux_t::T         # 6. TauxT - PRIORITÉ 4
    shannon_t::T      # 7. ShannonT - PRIORITÉ 5
    block_t::T        # 8. BlockT - PRIORITÉ 5

    # Constructeur interne avec validation
    function MetriquesTheorique{T}(
        cond_t::T, divkl_t::T, cross_t::T, metric_t::T,
        topo_t::T, taux_t::T, shannon_t::T, block_t::T
    ) where T<:AbstractFloat

        # Validation que toutes les métriques sont finies
        # Note: metric_t peut être négatif (différence de complexités pondérées)
        metriques = [cond_t, divkl_t, cross_t, metric_t, topo_t, taux_t, shannon_t, block_t]
        noms = ["cond_t", "divkl_t", "cross_t", "metric_t", "topo_t", "taux_t", "shannon_t", "block_t"]

        for (metrique, nom) in zip(metriques, noms)
            if !isfinite(metrique)
                throw(ArgumentError("$nom doit être finie, reçu: $metrique"))
            end

            # metric_t peut être négatif (mesure de changement de complexité)
            if nom != "metric_t" && metrique < 0
                throw(ArgumentError("$nom doit être ≥ 0, reçu: $metrique"))
            end
        end

        new{T}(cond_t, divkl_t, cross_t, metric_t, topo_t, taux_t, shannon_t, block_t)
    end
end

# Constructeur de convenance
MetriquesTheorique(args...) = MetriquesTheorique{Float64}(Float64.(args)...)

"""
    DifferentielsPredictifs{T<:AbstractFloat}

Structure immutable pour stocker les différentiels des 8 métriques théoriques
pour la prédiction. Calcule |métrique(n+1) - métrique(n)| pour chacune des
6 possibilités à la main n+1.

# Ordre de Priorité Prédictive
Les différentiels sont ordonnés selon leur importance prédictive :
1. **diff_cond_t** - |CondT(n+1) - CondT(n)| (PRIORITÉ 1)
2. **diff_divkl_t** - |DivKLT(n+1) - DivKLT(n)| (PRIORITÉ 2)
3. **diff_cross_t** - |CrossT(n+1) - CrossT(n)| (PRIORITÉ 2)
4. **diff_metric_t** - |MetricT(n+1) - MetricT(n)| (PRIORITÉ 3)
5. **diff_topo_t** - |TopoT(n+1) - TopoT(n)| (PRIORITÉ 3)
6. **diff_taux_t** - |TauxT(n+1) - TauxT(n)| (PRIORITÉ 4)
7. **diff_shannon_t** - |ShannonT(n+1) - ShannonT(n)| (PRIORITÉ 5)
8. **diff_block_t** - |BlockT(n+1) - BlockT(n)| (PRIORITÉ 5)

# Paramètres de Type
- `T<:AbstractFloat`: Type numérique pour les différentiels

# Exemples
```julia
diff = DifferentielsPredictifs{Float64}(0.1, 0.05, 0.08, 0.03, 0.06, 0.02, 0.04, 0.07)
```
"""
struct DifferentielsPredictifs{T<:AbstractFloat}
    # Différentiels des 8 métriques théoriques (ordre de priorité prédictive)
    diff_cond_t::T         # 1. |CondT(n+1) - CondT(n)| - PRIORITÉ 1
    diff_divkl_t::T        # 2. |DivKLT(n+1) - DivKLT(n)| - PRIORITÉ 2
    diff_cross_t::T        # 3. |CrossT(n+1) - CrossT(n)| - PRIORITÉ 2
    diff_metric_t::T       # 4. |MetricT(n+1) - MetricT(n)| - PRIORITÉ 3
    diff_topo_t::T         # 5. |TopoT(n+1) - TopoT(n)| - PRIORITÉ 3
    diff_taux_t::T         # 6. |TauxT(n+1) - TauxT(n)| - PRIORITÉ 4
    diff_shannon_t::T      # 7. |ShannonT(n+1) - ShannonT(n)| - PRIORITÉ 5
    diff_block_t::T        # 8. |BlockT(n+1) - BlockT(n)| - PRIORITÉ 5

    # Constructeur interne avec validation
    function DifferentielsPredictifs{T}(
        diff_cond_t::T, diff_divkl_t::T, diff_cross_t::T, diff_metric_t::T,
        diff_topo_t::T, diff_taux_t::T, diff_shannon_t::T, diff_block_t::T
    ) where T<:AbstractFloat

        # Validation que tous les différentiels sont finis et non-négatifs
        diffs = [diff_cond_t, diff_divkl_t, diff_cross_t, diff_metric_t,
                diff_topo_t, diff_taux_t, diff_shannon_t, diff_block_t]
        noms = ["diff_cond_t", "diff_divkl_t", "diff_cross_t", "diff_metric_t",
               "diff_topo_t", "diff_taux_t", "diff_shannon_t", "diff_block_t"]

        for (diff, nom) in zip(diffs, noms)
            if !isfinite(diff) || diff < 0
                throw(ArgumentError("$nom doit être fini et ≥ 0, reçu: $diff"))
            end
        end

        new{T}(diff_cond_t, diff_divkl_t, diff_cross_t, diff_metric_t,
               diff_topo_t, diff_taux_t, diff_shannon_t, diff_block_t)
    end
end

# Constructeur de convenance
DifferentielsPredictifs(args...) = DifferentielsPredictifs{Float64}(Float64.(args)...)

"""
    CalculateurDifferentielsPredictifs{T<:AbstractFloat}

Structure pour calculer les différentiels prédictifs des métriques théoriques.
Encapsule les formules théoriques et fournit une interface pour les calculs.

# Paramètres de Type
- `T<:AbstractFloat`: Type numérique pour les calculs

# Champs
- `formulas::FormulasTheoretical{T}`: Formules théoriques pour les calculs

# Exemples
```julia
calculateur = CalculateurDifferentielsPredictifs{Float64}()
calculateur_custom = CalculateurDifferentielsPredictifs{Float32}(2.0f0, 1f-10)
```
"""
struct CalculateurDifferentielsPredictifs{T<:AbstractFloat}
    formulas::FormulasTheoretical{T}

    function CalculateurDifferentielsPredictifs{T}(
        base::T = T(DEFAULT_BASE),
        epsilon::T = T(DEFAULT_EPSILON)
    ) where T<:AbstractFloat
        formulas = FormulasTheoretical{T}(base, epsilon)
        new{T}(formulas)
    end
end

# Constructeur de convenance
CalculateurDifferentielsPredictifs(base::Real = DEFAULT_BASE, epsilon::Real = DEFAULT_EPSILON) =
    CalculateurDifferentielsPredictifs{Float64}(Float64(base), Float64(epsilon))

"""
    PredictionResult

Structure immutable pour stocker le résultat complet de prédiction pour une main
avec toutes les métriques théoriques et différentiels calculés.

# Champs
- `main_actuelle::Int`: Numéro de la main actuelle
- `index5_actuel::String`: INDEX5 de la main actuelle
- `index1_suivant::Int`: INDEX1 calculé pour la main suivante
- `index5_observe::Union{String, Nothing}`: INDEX5 réellement observé à la main n+1
- `valeurs_possibles::Vector{String}`: Les 6 valeurs possibles d'INDEX5
- `metriques_par_possibilite::Vector{MetriquesTheorique{Float64}}`: Métriques pour chaque possibilité
- `differentiels_par_possibilite::Vector{DifferentielsPredictifs{Float64}}`: Différentiels pour chaque possibilité

# Exemples
```julia
prediction = PredictionResult(
    10, "0_A_BANKER", 1, "1_B_PLAYER",
    ["1_A_BANKER", "1_B_BANKER", "1_C_BANKER", "1_A_PLAYER", "1_B_PLAYER", "1_C_PLAYER"],
    metriques_vector, differentiels_vector
)
```
"""
struct PredictionResult
    main_actuelle::Int
    index5_actuel::String
    index1_suivant::Int
    index5_observe::Union{String, Nothing}
    valeurs_possibles::Vector{String}
    metriques_par_possibilite::Vector{MetriquesTheorique{Float64}}
    differentiels_par_possibilite::Vector{DifferentielsPredictifs{Float64}}

    # Constructeur interne avec validation
    function PredictionResult(
        main_actuelle::Int,
        index5_actuel::String,
        index1_suivant::Int,
        index5_observe::Union{String, Nothing},
        valeurs_possibles::Vector{String},
        metriques_par_possibilite::Vector{MetriquesTheorique{Float64}},
        differentiels_par_possibilite::Vector{DifferentielsPredictifs{Float64}}
    )
        # Validation de cohérence
        if main_actuelle < 1
            throw(ArgumentError("main_actuelle doit être ≥ 1, reçu: $main_actuelle"))
        end
        if index1_suivant ∉ [0, 1]
            throw(ArgumentError("index1_suivant doit être 0 ou 1, reçu: $index1_suivant"))
        end
        if length(valeurs_possibles) != 6
            throw(ArgumentError("valeurs_possibles doit contenir exactement 6 éléments"))
        end
        if length(metriques_par_possibilite) != length(valeurs_possibles)
            throw(ArgumentError("Nombre de métriques doit égaler le nombre de valeurs possibles"))
        end
        if length(differentiels_par_possibilite) != length(valeurs_possibles)
            throw(ArgumentError("Nombre de différentiels doit égaler le nombre de valeurs possibles"))
        end

        new(main_actuelle, index5_actuel, index1_suivant, index5_observe,
            valeurs_possibles, metriques_par_possibilite, differentiels_par_possibilite)
    end
end

# ═══════════════════════════════════════════════════════════════════════════════
# MODULE 2: UTILITAIRES DE GESTION DE FICHIERS
# ═══════════════════════════════════════════════════════════════════════════════

"""
    trouver_fichier_json_recent(dossier::String) -> String

Trouve le fichier JSON le plus récent dans le dossier spécifié.

# Arguments
- `dossier::String`: Chemin vers le dossier à analyser

# Returns
- `String`: Chemin complet vers le fichier JSON le plus récent

# Throws
- `ArgumentError`: Si le dossier n'existe pas ou ne contient aucun fichier JSON

# Exemples
```julia
chemin = trouver_fichier_json_recent("partie")
```
"""
function trouver_fichier_json_recent(dossier::String)
    if !isdir(dossier)
        throw(ArgumentError("Le dossier '$dossier' n'existe pas"))
    end

    fichiers_json = filter(f -> endswith(f, ".json"), readdir(dossier))

    if isempty(fichiers_json)
        throw(ArgumentError("Aucun fichier JSON trouvé dans le dossier '$dossier'"))
    end

    # Trier par date de modification (plus récent en premier)
    fichiers_avec_dates = [(f, stat(joinpath(dossier, f)).mtime) for f in fichiers_json]
    sort!(fichiers_avec_dates, by=x->x[2], rev=true)

    fichier_recent = fichiers_avec_dates[1][1]
    chemin_complet = joinpath(dossier, fichier_recent)

    println("📁 Fichier JSON le plus récent trouvé : $fichier_recent")
    return chemin_complet
end

"""
    charger_donnees_partie(chemin_fichier::String, numero_partie::Int) -> Vector{MainData}

Charge les données d'une partie spécifique depuis un fichier JSON avec cache optimisé.
Utilise le système CacheJSON.jl pour des performances maximales.

# Arguments
- `chemin_fichier::String`: Chemin vers le fichier JSON contenant les parties
- `numero_partie::Int`: Numéro de la partie à charger (≥ 1)

# Returns
- `Vector{MainData}`: Vecteur des mains valides de la partie

# Throws
- `ArgumentError`: Si le fichier est invalide, la partie n'existe pas, ou numero_partie < 1
- `SystemError`: Si le fichier ne peut pas être lu

# Performance
- **Cache intelligent** : Utilise LazyJSON + Mmap + Memoize pour performance optimale
- **Chargement instantané** : Pas de parsing complet du JSON
- **Mémoire minimale** : 99% moins d'allocations vs JSON.jl standard

# Exemples
```julia
mains = charger_donnees_partie("partie/data.json", 1)
```
"""
function charger_donnees_partie(chemin_fichier::String, numero_partie::Int)
    # Validation des paramètres
    if numero_partie < 1
        throw(ArgumentError("numero_partie doit être ≥ 1, reçu: $numero_partie"))
    end

    println("📖 Chargement optimisé du fichier : $chemin_fichier")
    println("🎯 Recherche de la partie numéro : $numero_partie")
    println("⚡ Utilisation du cache JSON haute performance...")

    try
        # Utiliser le système de cache optimisé CacheJSON.jl
        partie_data = CacheJSON.get_partie_cached(numero_partie, chemin_fichier)

        # Convertir les données CacheJSON vers le format MainData d'Analyseur.jl
        mains = Vector{MainData}()
        sizehint!(mains, length(partie_data.mains))

        for main_cache in partie_data.mains
            # Ignorer les mains avec des données manquantes
            if main_cache.index1 === "" ||
               main_cache.index2 === "" ||
               main_cache.index3 === ""
                continue
            end

            # Convertir CacheJSON.MainData vers PredicteurIndex5.MainData
            push!(mains, MainData(
                nothing,  # main_number (pas utilisé dans CacheJSON)
                nothing,  # manche_pb_number (pas utilisé dans CacheJSON)
                main_cache.index1,
                main_cache.index2,
                main_cache.index3,
                main_cache.index5
            ))
        end

        println("✅ Partie $numero_partie chargée avec cache : $(length(mains)) mains valides")

        return mains

    catch e
        println("❌ Erreur lors du chargement optimisé : $e")
        println("🔄 Tentative de fallback vers méthode standard...")

        # Fallback vers l'ancienne méthode en cas d'erreur
        return charger_donnees_partie_fallback(chemin_fichier, numero_partie)
    end
end

"""
    charger_donnees_partie_fallback(chemin_fichier::String, numero_partie::Int) -> Vector{MainData}

Méthode de fallback utilisant JSON.jl standard en cas d'erreur avec le cache.
"""
function charger_donnees_partie_fallback(chemin_fichier::String, numero_partie::Int)
    println("⚠️  Utilisation de la méthode de fallback JSON.jl standard")

    try
        contenu = read(chemin_fichier, String)
        donnees = JSON.parse(contenu)

        if !haskey(donnees, "parties_condensees") || isempty(donnees["parties_condensees"])
            throw(ArgumentError("Format de fichier invalide : 'parties_condensees' manquant ou vide"))
        end

        # Chercher la partie demandée
        partie_trouvee = nothing
        for partie in donnees["parties_condensees"]
            if partie["partie_number"] == numero_partie
                partie_trouvee = partie
                break
            end
        end

        if partie_trouvee === nothing
            throw(ArgumentError("Partie numéro $numero_partie non trouvée"))
        end

        mains_data = partie_trouvee["mains_condensees"]

        # Pré-allouer le vecteur pour optimiser les performances
        mains = Vector{MainData}()
        sizehint!(mains, length(mains_data))

        for main_data in mains_data
            # Ignorer les mains avec des données manquantes
            if main_data["main_number"] === nothing ||
               main_data["index1"] === "" ||
               main_data["index2"] === "" ||
               main_data["index3"] === ""
                continue
            end

            push!(mains, MainData(
                main_data["main_number"],
                main_data["manche_pb_number"],
                main_data["index1"],
                main_data["index2"],
                main_data["index3"],
                main_data["index5"]
            ))
        end

        println("✅ Partie $numero_partie chargée (fallback) : $(length(mains)) mains valides")
        return mains

    catch e
        println("❌ Erreur lors du chargement fallback : $e")
        rethrow(e)
    end
end

"""
    compter_parties_disponibles(chemin_fichier::String) -> Int

Compte le nombre de parties disponibles dans le fichier JSON avec cache optimisé.
Utilise le système CacheJSON.jl pour des performances maximales.

# Arguments
- `chemin_fichier::String`: Chemin vers le fichier JSON à analyser

# Returns
- `Int`: Nombre de parties disponibles (0 si erreur ou fichier vide)

# Performance
- **Cache intelligent** : Utilise LazyJSON + Mmap pour accès instantané
- **Pas de parsing complet** : Accès direct aux métadonnées
- **Mémoire minimale** : Pas de copie du fichier en mémoire

# Exemples
```julia
nb_parties = compter_parties_disponibles("partie/data.json")
```
"""
function compter_parties_disponibles(chemin_fichier::String)
    try
        println("📊 Comptage optimisé des parties dans : $chemin_fichier")

        # Utiliser le système de cache optimisé CacheJSON.jl
        nb_parties = CacheJSON.count_parties_in_dataset(chemin_fichier)

        println("✅ Nombre de parties trouvées avec cache : $nb_parties")

        return nb_parties

    catch e
        println("❌ Erreur lors du comptage optimisé : $e")
        println("🔄 Tentative de fallback vers méthode standard...")

        # Fallback vers l'ancienne méthode en cas d'erreur
        return compter_parties_disponibles_fallback(chemin_fichier)
    end
end

"""
    compter_parties_disponibles_fallback(chemin_fichier::String) -> Int

Méthode de fallback utilisant JSON.jl standard en cas d'erreur avec le cache.
"""
function compter_parties_disponibles_fallback(chemin_fichier::String)
    println("⚠️  Utilisation de la méthode de fallback JSON.jl standard")

    try
        contenu = read(chemin_fichier, String)
        donnees = JSON.parse(contenu)

        if !haskey(donnees, "parties_condensees")
            return 0
        end

        nb_parties = length(donnees["parties_condensees"])
        println("✅ Nombre de parties trouvées (fallback) : $nb_parties")
        return nb_parties

    catch e
        println("❌ Erreur lors du comptage fallback : $e")
        return 0
    end
end

# ═══════════════════════════════════════════════════════════════════════════════
# MODULE 3: ORCHESTRATEUR DE CALCULS DE MÉTRIQUES
# ═══════════════════════════════════════════════════════════════════════════════

"""
    calculer_toutes_metriques_theoriques(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> MetriquesTheorique{T}

Orchestrateur principal pour le calcul coordonné des 8 métriques théoriques.
Calcule toutes les métriques pour une séquence donnée jusqu'à la main n en respectant
l'ordre de priorité prédictive et les dépendances entre métriques.

# Arguments
- `formulas::FormulasTheoretical{T}`: Structure contenant les formules théoriques
- `sequence::Vector{String}`: Séquence INDEX5 jusqu'à la main n
- `n::Int`: Numéro de la main courante (≥ 1)

# Returns
- `MetriquesTheorique{T}`: Structure contenant les 8 métriques calculées

# Notes
Les modules de métriques sont automatiquement importés dans ce programme unifié.
Tous les 8 modules sont intégrés et prêts à être utilisés.

# Ordre de Calcul (par priorité prédictive)
1. **CondT** - Entropie Conditionnelle (PRIORITÉ 1)
2. **DivKLT** - Divergence KL (PRIORITÉ 2)
3. **CrossT** - Entropie Croisée (PRIORITÉ 2)
4. **MetricT** - Entropie Métrique (PRIORITÉ 3)
5. **TopoT** - Entropie Topologique (PRIORITÉ 3)
6. **TauxT** - Taux d'Entropie (PRIORITÉ 4)
7. **ShannonT** - Entropie de Shannon (PRIORITÉ 5)
8. **BlockT** - Entropie de Bloc (PRIORITÉ 5)

# Exemples
```julia
formulas = FormulasTheoretical{Float64}()
sequence = ["0_A_BANKER", "1_B_PLAYER", "0_C_BANKER"]
metriques = calculer_toutes_metriques_theoriques(formulas, sequence, 3)
```
"""
function calculer_toutes_metriques_theoriques(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat

    # Validation des paramètres
    if n < 1
        throw(ArgumentError("n doit être ≥ 1, reçu: $n"))
    end
    if n > length(sequence)
        throw(ArgumentError("n ($n) ne peut pas être supérieur à la longueur de la séquence ($(length(sequence)))"))
    end
    if isempty(sequence)
        throw(ArgumentError("La séquence ne peut pas être vide"))
    end

    # Initialiser la séquence complète pour les calculs de probabilités conditionnelles empiriques
    formulas.sequence_complete = sequence

    # Calcul des 8 métriques dans l'ordre de priorité prédictive
    # UTILISE LES MODULES INTÉGRÉS - TOUS LES IMPORTS SONT CONFIGURÉS
    cond_t = CondT.calculer_formule5B_conditionnelle_theo(formulas, sequence, n)              # 1. PRIORITÉ 1
    divkl_t = DivKLT.calculer_formule6B_divergence_kl_theo(formulas, sequence, n)              # 2. PRIORITÉ 2
    cross_t = CrossT.calculer_formule8B_entropie_croisee_theo(formulas, sequence, n)           # 3. PRIORITÉ 2
    metric_t = MetricT.calculer_formule4B_entropie_metrique_theo(formulas, sequence, n)         # 4. PRIORITÉ 3
    topo_t = TopoT.calculer_formule9B_entropie_topologique_theo(formulas, sequence, n)        # 5. PRIORITÉ 3
    taux_t = TauxT.calculer_formule3B_taux_entropie_theo(formulas, sequence, n)               # 6. PRIORITÉ 4
    shannon_t = ShannonT.calculer_formule1B_shannon_jointe_theo(formulas, sequence, n)           # 7. PRIORITÉ 5
    block_t = BlockT.calculer_formule10B_block_cumulative_theo(formulas, sequence, n)          # 8. PRIORITÉ 5

    return MetriquesTheorique{T}(
        cond_t, divkl_t, cross_t, metric_t,
        topo_t, taux_t, shannon_t, block_t
    )
end

# ═══════════════════════════════════════════════════════════════════════════════
# MODULE 4: CALCUL DES DIFFÉRENTIELS PRÉDICTIFS
# ═══════════════════════════════════════════════════════════════════════════════

"""
    calculer_differentiels_predictifs(calculateur::CalculateurDifferentielsPredictifs{T}, metriques_n::MetriquesTheorique{T}, metriques_n_plus_1::MetriquesTheorique{T}) where T -> DifferentielsPredictifs{T}

Calcule les différentiels prédictifs entre les métriques de la main n et n+1.
Utilise la formule : |métrique(n+1) - métrique(n)| pour chacune des 8 métriques théoriques.

# Arguments
- `calculateur::CalculateurDifferentielsPredictifs{T}`: Calculateur de différentiels
- `metriques_n::MetriquesTheorique{T}`: Métriques à la main n (état actuel)
- `metriques_n_plus_1::MetriquesTheorique{T}`: Métriques à la main n+1 (état hypothétique)

# Returns
- `DifferentielsPredictifs{T}`: Structure contenant les 8 différentiels calculés

# Exemples
```julia
calculateur = CalculateurDifferentielsPredictifs{Float64}()
diff = calculer_differentiels_predictifs(calculateur, metriques_n, metriques_n_plus_1)
```
"""
function calculer_differentiels_predictifs(
    calculateur::CalculateurDifferentielsPredictifs{T},
    metriques_n::MetriquesTheorique{T},
    metriques_n_plus_1::MetriquesTheorique{T}
) where T<:AbstractFloat

    # Calculer les différentiels absolus |métrique(n+1) - métrique(n)|
    # (ordre de priorité prédictive)
    diff_cond_t = abs(metriques_n_plus_1.cond_t - metriques_n.cond_t)             # 1. PRIORITÉ 1
    diff_divkl_t = abs(metriques_n_plus_1.divkl_t - metriques_n.divkl_t)          # 2. PRIORITÉ 2
    diff_cross_t = abs(metriques_n_plus_1.cross_t - metriques_n.cross_t)          # 3. PRIORITÉ 2
    diff_metric_t = abs(metriques_n_plus_1.metric_t - metriques_n.metric_t)       # 4. PRIORITÉ 3
    diff_topo_t = abs(metriques_n_plus_1.topo_t - metriques_n.topo_t)             # 5. PRIORITÉ 3
    diff_taux_t = abs(metriques_n_plus_1.taux_t - metriques_n.taux_t)             # 6. PRIORITÉ 4
    diff_shannon_t = abs(metriques_n_plus_1.shannon_t - metriques_n.shannon_t)    # 7. PRIORITÉ 5
    diff_block_t = abs(metriques_n_plus_1.block_t - metriques_n.block_t)          # 8. PRIORITÉ 5

    return DifferentielsPredictifs{T}(
        diff_cond_t, diff_divkl_t, diff_cross_t, diff_metric_t,
        diff_topo_t, diff_taux_t, diff_shannon_t, diff_block_t
    )
end

"""
    calculer_differentiels_pour_possibilites(calculateur::CalculateurDifferentielsPredictifs{T}, sequence_n::Vector{String}, valeurs_possibles::Vector{String}) where T -> Vector{DifferentielsPredictifs{T}}

Calcule les différentiels prédictifs pour toutes les possibilités à la main n+1.
Optimisé pour traiter efficacement les 6 valeurs possibles d'INDEX5.

# Arguments
- `calculateur::CalculateurDifferentielsPredictifs{T}`: Calculateur de différentiels
- `sequence_n::Vector{String}`: Séquence INDEX5 jusqu'à la main n
- `valeurs_possibles::Vector{String}`: Valeurs possibles pour la main n+1 (typiquement 6)

# Returns
- `Vector{DifferentielsPredictifs{T}}`: Vecteur des différentiels pour chaque possibilité

# Exemples
```julia
calculateur = CalculateurDifferentielsPredictifs{Float64}()
sequence = ["0_A_BANKER", "1_B_PLAYER"]
possibilites = ["0_A_BANKER", "0_B_BANKER", "0_C_BANKER", "0_A_PLAYER", "0_B_PLAYER", "0_C_PLAYER"]
differentiels = calculer_differentiels_pour_possibilites(calculateur, sequence, possibilites)
```
"""
function calculer_differentiels_pour_possibilites(
    calculateur::CalculateurDifferentielsPredictifs{T},
    sequence_n::Vector{String},
    valeurs_possibles::Vector{String}
) where T<:AbstractFloat

    # Validation des paramètres
    if isempty(sequence_n)
        throw(ArgumentError("sequence_n ne peut pas être vide"))
    end
    if isempty(valeurs_possibles)
        throw(ArgumentError("valeurs_possibles ne peut pas être vide"))
    end

    n = length(sequence_n)

    # Calculer les métriques pour la main n (état actuel)
    metriques_n = calculer_toutes_metriques_theoriques(calculateur.formulas, sequence_n, n)

    # Pré-allouer le vecteur de résultats pour optimiser les performances
    differentiels = Vector{DifferentielsPredictifs{T}}()
    sizehint!(differentiels, length(valeurs_possibles))

    for valeur_possible in valeurs_possibles
        # Créer la séquence hypothétique avec cette valeur ajoutée
        sequence_n_plus_1 = vcat(sequence_n, [valeur_possible])

        # Calculer les métriques pour la main n+1 (état hypothétique)
        metriques_n_plus_1 = calculer_toutes_metriques_theoriques(calculateur.formulas, sequence_n_plus_1, n + 1)

        # Calculer les différentiels |métrique(n+1) - métrique(n)|
        diff = calculer_differentiels_predictifs(calculateur, metriques_n, metriques_n_plus_1)
        push!(differentiels, diff)
    end

    return differentiels
end

# ═══════════════════════════════════════════════════════════════════════════════
# MODULE 5: LOGIQUE DE PRÉDICTION
# ═══════════════════════════════════════════════════════════════════════════════

"""
    calculer_index1_suivant(index1_actuel::Int, index2_actuel::String) -> Int

Calcule la valeur d'INDEX1 pour la main suivante selon les règles de transition.

# Règles de Transition
- **INDEX2 = "C"**: INDEX1 s'inverse (0→1, 1→0)
- **INDEX2 = "A"**: INDEX1 reste identique
- **INDEX2 = "B"**: INDEX1 reste identique

# Arguments
- `index1_actuel::Int`: Valeur actuelle d'INDEX1 (0 ou 1)
- `index2_actuel::String`: Valeur actuelle d'INDEX2 ("A", "B", ou "C")

# Returns
- `Int`: Nouvelle valeur d'INDEX1 pour la main suivante (0 ou 1)

# Throws
- `ArgumentError`: Si index1_actuel ∉ [0,1] ou index2_actuel ∉ ["A","B","C"]

# Exemples
```julia
index1_suivant = calculer_index1_suivant(0, "C")  # Retourne 1
index1_suivant = calculer_index1_suivant(1, "A")  # Retourne 1
```
"""
function calculer_index1_suivant(index1_actuel::Int, index2_actuel::String)
    # Validation des paramètres
    if index1_actuel ∉ [0, 1]
        throw(ArgumentError("index1_actuel doit être 0 ou 1, reçu: $index1_actuel"))
    end
    if index2_actuel ∉ INDEX2_POSSIBLES
        throw(ArgumentError("index2_actuel doit être A, B ou C, reçu: '$index2_actuel'"))
    end

    if index2_actuel == "C"
        # Règle C : INDEX1 s'inverse
        return index1_actuel == 0 ? 1 : 0
    elseif index2_actuel == "A" || index2_actuel == "B"
        # Règles A et B : INDEX1 reste identique
        return index1_actuel
    else
        # Cette condition ne devrait jamais être atteinte grâce à la validation
        throw(ArgumentError("INDEX2 invalide : '$index2_actuel'. Doit être A, B ou C"))
    end
end

"""
    generer_valeurs_possibles(index1_suivant::Int) -> Vector{String}

Génère les 6 valeurs possibles d'INDEX5 pour la main suivante.
Exclut les possibilités TIE à l'INDEX3 pour se concentrer sur les prédictions BANKER/PLAYER.

# Ordre de Génération
1. **BANKER**: A, B, C (3 valeurs)
2. **PLAYER**: A, B, C (3 valeurs)

# Arguments
- `index1_suivant::Int`: Valeur d'INDEX1 pour la main suivante (0 ou 1)

# Returns
- `Vector{String}`: Vecteur de 6 valeurs INDEX5 possibles

# Format des Valeurs
Chaque valeur suit le format: `"INDEX1_INDEX2_INDEX3"`

# Throws
- `ArgumentError`: Si index1_suivant ∉ [0,1]

# Exemples
```julia
possibilites = generer_valeurs_possibles(1)
# Retourne: ["1_A_BANKER", "1_B_BANKER", "1_C_BANKER", "1_A_PLAYER", "1_B_PLAYER", "1_C_PLAYER"]
```
"""
function generer_valeurs_possibles(index1_suivant::Int)
    # Validation des paramètres
    if index1_suivant ∉ [0, 1]
        throw(ArgumentError("index1_suivant doit être 0 ou 1, reçu: $index1_suivant"))
    end

    # Pré-allouer le vecteur pour optimiser les performances
    valeurs_possibles = Vector{String}()
    sizehint!(valeurs_possibles, 6)  # Exactement 6 valeurs attendues

    # D'abord tous les BANKER : A, B, C
    for index2 in INDEX2_POSSIBLES
        index5 = "$(index1_suivant)_$(index2)_BANKER"
        push!(valeurs_possibles, index5)
    end

    # Puis tous les PLAYER : A, B, C
    for index2 in INDEX2_POSSIBLES
        index5 = "$(index1_suivant)_$(index2)_PLAYER"
        push!(valeurs_possibles, index5)
    end

    return valeurs_possibles
end

"""
    predire_main_suivante(main_actuelle::MainData, sequence_jusqu_n::Vector{String}, index5_observe::Union{String, Nothing} = nothing) -> PredictionResult

Prédit les valeurs possibles pour la main suivante avec calcul des métriques théoriques.
"""
function predire_main_suivante(main_actuelle::MainData, sequence_jusqu_n::Vector{String}, index5_observe::Union{String, Nothing} = nothing)
    # Calculer INDEX1 pour la main suivante
    index1_suivant = calculer_index1_suivant(main_actuelle.index1, main_actuelle.index2)

    # Générer toutes les valeurs possibles
    valeurs_possibles = generer_valeurs_possibles(index1_suivant)

    # Initialiser les structures pour les calculs d'entropie et différentiels
    formulas = FormulasTheoretical{Float64}()
    calculateur_diff = CalculateurDifferentielsPredictifs{Float64}()

    # Calculer les métriques pour la main n (état actuel)
    n = length(sequence_jusqu_n)
    metriques_n = calculer_toutes_metriques_theoriques(formulas, sequence_jusqu_n, n)

    # Calculer les métriques et différentiels pour chaque possibilité
    metriques_par_possibilite = MetriquesTheorique{Float64}[]
    differentiels_par_possibilite = DifferentielsPredictifs{Float64}[]

    for valeur_possible in valeurs_possibles
        # Créer une séquence hypothétique avec cette valeur ajoutée
        sequence_hypothetique = vcat(sequence_jusqu_n, [valeur_possible])
        n_hypothetique = length(sequence_hypothetique)

        # Calculer toutes les métriques théoriques pour cette séquence hypothétique (main n+1)
        metriques_n_plus_1 = calculer_toutes_metriques_theoriques(formulas, sequence_hypothetique, n_hypothetique)
        push!(metriques_par_possibilite, metriques_n_plus_1)

        # Calculer les différentiels |métrique(n+1) - métrique(n)|
        diff = calculer_differentiels_predictifs(calculateur_diff, metriques_n, metriques_n_plus_1)
        push!(differentiels_par_possibilite, diff)
    end

    return PredictionResult(
        main_actuelle.main_number,
        main_actuelle.index5,
        index1_suivant,
        index5_observe,
        valeurs_possibles,
        metriques_par_possibilite,
        differentiels_par_possibilite
    )
end

# ═══════════════════════════════════════════════════════════════════════════════
# AFFICHAGE ET INTERFACE
# ═══════════════════════════════════════════════════════════════════════════════

"""
    exporter_prediction_vers_fichier(fichier::IO, prediction::PredictionResult)

Exporte une prédiction vers un fichier texte avec le format détaillé.
"""
function exporter_prediction_vers_fichier(fichier::IO, prediction::PredictionResult)
    println(fichier, "="^100)
    println(fichier, "🎯 PRÉDICTION POUR LA MAIN $(prediction.main_actuelle + 1)")
    println(fichier, "="^100)
    println(fichier, "📊 Main actuelle : $(prediction.main_actuelle)")
    println(fichier, "🎲 INDEX5 actuel : $(prediction.index5_actuel)")
    println(fichier, "🔄 INDEX1 suivant : $(prediction.index1_suivant)")

    # Afficher l'INDEX5 observé (toujours afficher la ligne)
    if prediction.index5_observe !== nothing
        println(fichier, "👁️  INDEX5 observé : $(prediction.index5_observe)")
    else
        println(fichier, "👁️  INDEX5 observé : (non disponible)")
    end

    println(fichier, "\n📋 Les 6 valeurs possibles avec leurs métriques théoriques et différentiels :")

    # En-tête du tableau avec métriques et différentiels selon l'ordre de priorité prédictive (InfoMutT et CondDecT supprimés)
    # Chaque bloc a une largeur fixe de 16 caractères pour assurer l'alignement parfait
    header = @sprintf("%-15s %-16s %-16s %-16s %-16s %-16s %-16s %-16s %-16s",
        "INDEX5", "CondT |Diff", "DivKLT |Diff", "CrossT |Diff", "MetricT |Diff",
        "TopoT |Diff", "TauxT |Diff", "ShannonT|Diff", "BlockT |Diff")
    println(fichier, header)
    println(fichier, "-"^length(header))

    for (i, (valeur, metriques, differentiels)) in enumerate(zip(prediction.valeurs_possibles, prediction.metriques_par_possibilite, prediction.differentiels_par_possibilite))
        # Formater chaque bloc individuellement selon l'ordre de priorité prédictive (InfoMutT et CondDecT supprimés)
        bloc1 = @sprintf("%-6.4f|%-8.4f", metriques.cond_t, differentiels.diff_cond_t)          # 1. CondT
        bloc2 = @sprintf("%-7.4f|%-8.4f", metriques.divkl_t, differentiels.diff_divkl_t)        # 2. DivKLT
        bloc3 = @sprintf("%-7.4f|%-8.4f", metriques.cross_t, differentiels.diff_cross_t)        # 3. CrossT
        bloc4 = @sprintf("%-8.4f|%-7.4f", metriques.metric_t, differentiels.diff_metric_t)      # 4. MetricT
        bloc5 = @sprintf("%-6.4f|%-8.4f", metriques.topo_t, differentiels.diff_topo_t)          # 5. TopoT
        bloc6 = @sprintf("%-6.4f|%-8.4f", metriques.taux_t, differentiels.diff_taux_t)          # 6. TauxT
        bloc7 = @sprintf("%-8.4f|%-7.4f", metriques.shannon_t, differentiels.diff_shannon_t)    # 7. ShannonT
        bloc8 = @sprintf("%-9.6f|%-8.4f", metriques.block_t, differentiels.diff_block_t)        # 8. BlockT

        ligne = @sprintf("%-15s %-16s %-16s %-16s %-16s %-16s %-16s %-16s %-16s",
            valeur, bloc1, bloc2, bloc3, bloc4, bloc5, bloc6, bloc7, bloc8)
        println(fichier, ligne)
    end

    println(fichier, "-"^length(header))
    println(fichier, "✨ Total : $(length(prediction.valeurs_possibles)) valeurs possibles avec métriques et différentiels calculés")
    println(fichier)
end

"""
    afficher_prediction(prediction::PredictionResult, compact::Bool=false, avec_metriques::Bool=false)

Affiche le résultat de prédiction de manière formatée.
"""
function afficher_prediction(prediction::PredictionResult, compact::Bool=false, avec_metriques::Bool=false)
    if compact && !avec_metriques
        # Affichage compact pour les listes longues
        println("\n📊 Main $(prediction.main_actuelle) → Main $(prediction.main_actuelle + 1)")
        println("   INDEX5 actuel: $(prediction.index5_actuel) | INDEX1 suivant: $(prediction.index1_suivant)")
        print("   Valeurs possibles: ")
        println(join(prediction.valeurs_possibles, " "))
    elseif avec_metriques
        # Affichage avec métriques théoriques
        println("\n" * "="^100)
        println("🎯 PRÉDICTION AVEC MÉTRIQUES POUR LA MAIN $(prediction.main_actuelle + 1)")
        println("="^100)
        println("📊 Main actuelle : $(prediction.main_actuelle)")
        println("🎲 INDEX5 actuel : $(prediction.index5_actuel)")
        println("🔄 INDEX1 suivant : $(prediction.index1_suivant)")

        # Afficher l'INDEX5 observé (toujours afficher la ligne)
        if prediction.index5_observe !== nothing
            println("👁️  INDEX5 observé : $(prediction.index5_observe)")
        else
            println("👁️  INDEX5 observé : (non disponible)")
        end

        println("\n📋 Les 6 valeurs possibles avec leurs métriques théoriques et différentiels :")
        println("-"^200)

        # En-tête du tableau avec métriques et différentiels selon l'ordre de priorité prédictive (InfoMutT et CondDecT supprimés)
        println(@sprintf("%-15s %8s | %4s %8s | %4s %8s | %4s %8s | %4s %8s | %4s %8s | %4s %8s | %4s %8s | %4s",
            "INDEX5", "CondT", "Diff", "DivKLT", "Diff", "CrossT", "Diff", "MetricT", "Diff",
            "TopoT", "Diff", "TauxT", "Diff", "ShannonT", "Diff", "BlockT", "Diff"))
        println("-"^160)

        for (i, (valeur, metriques, differentiels)) in enumerate(zip(prediction.valeurs_possibles, prediction.metriques_par_possibilite, prediction.differentiels_par_possibilite))
            println(@sprintf("%-15s %8.4f | %4.4f %8.4f | %4.4f %8.4f | %4.4f %8.4f | %4.4f %8.4f | %4.4f %8.4f | %4.4f %8.4f | %4.4f %8.4f | %4.4f",
                valeur,
                metriques.cond_t, differentiels.diff_cond_t,            # 1. CondT - PRIORITÉ 1
                metriques.divkl_t, differentiels.diff_divkl_t,          # 2. DivKLT - PRIORITÉ 2
                metriques.cross_t, differentiels.diff_cross_t,          # 3. CrossT - PRIORITÉ 2
                metriques.metric_t, differentiels.diff_metric_t,        # 4. MetricT - PRIORITÉ 3
                metriques.topo_t, differentiels.diff_topo_t,            # 5. TopoT - PRIORITÉ 3
                metriques.taux_t, differentiels.diff_taux_t,            # 6. TauxT - PRIORITÉ 4
                metriques.shannon_t, differentiels.diff_shannon_t,      # 7. ShannonT - PRIORITÉ 5
                metriques.block_t, differentiels.diff_block_t))         # 8. BlockT - PRIORITÉ 5
        end

        println("-"^200)
        println("✨ Total : $(length(prediction.valeurs_possibles)) valeurs possibles avec métriques et différentiels calculés")
    else
        # Affichage détaillé standard
        println("\n" * "="^80)
        println("🎯 PRÉDICTION POUR LA MAIN $(prediction.main_actuelle + 1)")
        println("="^80)
        println("📊 Main actuelle : $(prediction.main_actuelle)")
        println("🎲 INDEX5 actuel : $(prediction.index5_actuel)")
        println("🔄 INDEX1 suivant : $(prediction.index1_suivant)")
        println("\n📋 Les 6 valeurs possibles d'INDEX5 pour la main $(prediction.main_actuelle + 1) :")
        println("-"^50)

        for (i, valeur) in enumerate(prediction.valeurs_possibles)
            println(@sprintf("%2d. %s", i, valeur))
        end

        println("-"^50)
        println("✨ Total : $(length(prediction.valeurs_possibles)) valeurs possibles")
    end
end

"""
    afficher_regles()

Affiche les règles de transition d'INDEX1.
"""
function afficher_regles()
    println("\n📜 RÈGLES DE TRANSITION INDEX1 :")
    println("="^50)
    println("• Si INDEX2 = C → INDEX1 s'inverse (0→1, 1→0)")
    println("• Si INDEX2 = A → INDEX1 reste identique")
    println("• Si INDEX2 = B → INDEX1 reste identique")
    println("="^50)
end

# ═══════════════════════════════════════════════════════════════════════════════
# PROGRAMME PRINCIPAL
# ═══════════════════════════════════════════════════════════════════════════════

"""
    main()

Fonction principale du programme.
"""
function main()
    println("🚀 PRÉDICTEUR INDEX5 - DÉMARRAGE")
    println("="^60)

    try
        # 0. Initialiser le système de cache optimisé
        println("⚡ Initialisation du système de cache JSON haute performance...")
        CacheJSON.configure_cache(
            max_parties = 50,      # Cache pour 50 parties (optimisé pour 28GB RAM)
            max_files = 3,         # Cache pour 3 fichiers JSON max
            threading = true,      # Utiliser les 8 cœurs CPU
            stats = true           # Activer les statistiques de performance
        )
        println("✅ Cache configuré pour 8 cœurs CPU et 28GB RAM")

        # 1. Charger automatiquement le fichier JSON le plus récent
        dossier_partie = "partie"
        chemin_fichier = trouver_fichier_json_recent(dossier_partie)

        # 2. Compter les parties disponibles
        nb_parties = compter_parties_disponibles(chemin_fichier)
        println("📊 Nombre de parties disponibles : $nb_parties")

        if nb_parties == 0
            println("❌ Aucune partie trouvée dans le fichier")
            return
        end

        # 3. Demander à l'utilisateur de choisir une partie
        println("\n🎯 SÉLECTION DE LA PARTIE")
        println("="^50)

        numero_partie = 0
        while true
            print("➤ Choisissez le numéro de partie (1-$nb_parties) : ")
            input = strip(readline())

            try
                numero_partie = parse(Int, input)
                if numero_partie >= 1 && numero_partie <= nb_parties
                    break
                else
                    println("❌ Numéro invalide. Doit être entre 1 et $nb_parties")
                end
            catch
                println("❌ Veuillez entrer un numéro valide")
            end
        end

        # 4. Charger la partie sélectionnée
        mains = charger_donnees_partie(chemin_fichier, numero_partie)

        if isempty(mains)
            println("❌ Aucune main valide trouvée dans la partie $numero_partie")
            return
        end

        # 5. Les règles sont connues, pas besoin de les afficher
        # afficher_regles()

        # 6. Demander le mode d'affichage
        println("\n🎯 MODE D'AFFICHAGE")
        println("="^50)
        println("1. Affichage avec métriques théoriques et export automatique")

        mode_affichage = 1
        while true
            print("➤ Appuyez sur Entrée pour continuer avec l'export automatique : ")
            input = strip(readline())

            # Accepter n'importe quelle entrée (y compris vide) pour continuer
            mode_affichage = 1
            break
        end

        avec_metriques = true  # Toujours avec métriques maintenant

        # 7. Générer les prédictions pour toutes les mains 3 à 59
        println("\n🎯 GÉNÉRATION DES PRÉDICTIONS POUR LA PARTIE $numero_partie (mains 3 à 59)")
        println("="^80)

        # Calculer le nombre maximum de mains à traiter (59 ou moins si le fichier en contient moins)
        max_mains = min(59, length(mains))

        println("📊 Traitement de $max_mains mains (à partir de la main 3)...")
        println("ℹ️  Les mains 1 et 2 sont ignorées car BlockT et TauxT nécessitent au moins 3 mains")
        if avec_metriques
            println("⚡ Calcul des 10 métriques théoriques pour chaque possibilité...")
        end

        # Construire la séquence INDEX5 pour les calculs d'entropie
        sequence_index5 = [main.index5 for main in mains]

        # Générer toutes les prédictions (commencer à la main 3)
        predictions = PredictionResult[]

        for i in 3:max_mains
            main_actuelle = mains[i]
            # Utiliser la séquence jusqu'à la main actuelle pour les calculs d'entropie
            sequence_jusqu_n = sequence_index5[1:i]

            # Récupérer l'INDEX5 observé à la main n+1 (si disponible)
            index5_observe = nothing
            if i < length(mains)  # S'il y a une main suivante
                index5_observe = mains[i + 1].index5
            end

            # Toujours calculer avec métriques et différentiels
            prediction = predire_main_suivante(main_actuelle, sequence_jusqu_n, index5_observe)

            push!(predictions, prediction)
        end

        # Exporter automatiquement vers un fichier texte
        nom_fichier_export = "predictions_partie_$(numero_partie)_$(Dates.format(now(), "yyyymmdd_HHMMSS")).txt"

        println("📁 Export automatique vers : $nom_fichier_export")

        open(nom_fichier_export, "w") do fichier
            # En-tête du fichier
            println(fichier, "PRÉDICTIONS INDEX5 - PARTIE $numero_partie (MAINS 3 À $max_mains)")
            println(fichier, "="^80)
            println(fichier, "Généré le : $(Dates.format(now(), "dd/mm/yyyy à HH:MM:SS"))")
            println(fichier, "Nombre de prédictions : $(length(predictions))")
            println(fichier, "Métriques calculées : 10 métriques théoriques avec différentiels")
            println(fichier, "Note : Mains 1 et 2 ignorées (BlockT et TauxT nécessitent ≥ 3 mains)")
            println(fichier, "="^80)
            println(fichier)

            # Exporter chaque prédiction
            for prediction in predictions
                exporter_prediction_vers_fichier(fichier, prediction)
            end

            # Pied de page
            println(fichier)
            println(fichier, "="^80)
            println(fichier, "FIN DU RAPPORT - $(length(predictions)) prédictions exportées")
            println(fichier, "="^80)
        end

        # Résumé final
        println("\n" * "="^80)
        println("✅ RÉSUMÉ FINAL - PARTIE $numero_partie")
        println("="^80)
        println("📈 Nombre total de prédictions générées : $(length(predictions)) (mains 3 à $max_mains)")
        println("ℹ️  Mains 1 et 2 ignorées (BlockT et TauxT nécessitent ≥ 3 mains)")
        println("🎲 Chaque prédiction contient 6 valeurs possibles d'INDEX5 (TIE exclu)")
        println("📊 10 métriques théoriques avec différentiels calculées pour chaque possibilité")
        println("📋 Total de calculs de métriques : $(length(predictions) * 6 * 10)")
        println("📋 Total de calculs de différentiels : $(length(predictions) * 6 * 10)")
        println("📁 Résultats exportés automatiquement vers : $nom_fichier_export")
        println("="^80)

        # Afficher les statistiques finales du cache
        println("\n🚀 STATISTIQUES FINALES DU CACHE JSON")
        println("="^60)
        CacheJSON.print_cache_stats()

    catch e
        println("💥 Erreur fatale : $e")

        # Afficher les statistiques du cache même en cas d'erreur
        try
            println("\n🚀 STATISTIQUES DU CACHE (malgré l'erreur)")
            println("="^50)
            CacheJSON.print_cache_stats()
        catch cache_error
            println("⚠️  Impossible d'afficher les statistiques du cache : $cache_error")
        end

        return 1
    end

    # Stats cache
    CacheJSON.print_cache_stats()
    return 0
end

# ═══════════════════════════════════════════════════════════════════════════════
# MODULE 8: PRÉDICTEUR AVANCÉ BASÉ SUR LES DIFFÉRENTIELS
# ═══════════════════════════════════════════════════════════════════════════════

"""
    PredicteurDifferentiels

Structure mutable pour les prédictions avancées basées sur l'analyse des différentiels.
Maintient des statistiques de performance et utilise la somme des différentiels
pour prédire INDEX3 (BANKER vs PLAYER).

# Champs
- `nom_fichier_resultats::String`: Nom du fichier pour sauvegarder les résultats
- `predictions_correctes::Ref{Int}`: Compteur de prédictions correctes
- `predictions_incorrectes::Ref{Int}`: Compteur de prédictions incorrectes
- `predictions_ties::Ref{Int}`: Compteur de prédictions TIE
- `correctes_consecutives::Ref{Int}`: Séquence actuelle de prédictions correctes
- `incorrectes_consecutives::Ref{Int}`: Séquence actuelle de prédictions incorrectes
- `max_correctes_consecutives::Ref{Int}`: Record de prédictions correctes consécutives
- `max_incorrectes_consecutives::Ref{Int}`: Record de prédictions incorrectes consécutives

# Exemples
```julia
predicteur = PredicteurDifferentiels("resultats_predictions.txt")
```
"""
struct PredicteurDifferentiels
    nom_fichier_resultats::String
    predictions_correctes::Ref{Int}
    predictions_incorrectes::Ref{Int}
    predictions_ties::Ref{Int}
    correctes_consecutives::Ref{Int}
    incorrectes_consecutives::Ref{Int}
    max_correctes_consecutives::Ref{Int}
    max_incorrectes_consecutives::Ref{Int}

    function PredicteurDifferentiels(nom_fichier::String)
        if isempty(nom_fichier)
            throw(ArgumentError("nom_fichier ne peut pas être vide"))
        end
        new(nom_fichier, Ref(0), Ref(0), Ref(0), Ref(0), Ref(0), Ref(0), Ref(0))
    end
end

"""
    calculer_somme_differentiels_groupe(resultats_simulation, groupe::String) -> Float64

Calcule la somme des 8 différentiels pour un groupe spécifique (BANKER ou PLAYER).
Utilise les différentiels des métriques théoriques pour évaluer l'impact d'un groupe.

# Arguments
- `resultats_simulation`: Résultats de simulation contenant les métriques par INDEX5
- `groupe::String`: Groupe à analyser ("BANKER" ou "PLAYER")

# Returns
- `Float64`: Somme totale des différentiels pour le groupe spécifié

# Métriques Incluses
Les 8 différentiels suivants sont additionnés :
- Diff_CondT, Diff_DivKLT, Diff_CrossT, Diff_MetricT
- Diff_TopoT, Diff_TauxT, Diff_ShannonT, Diff_BlockT

# Exemples
```julia
somme_banker = calculer_somme_differentiels_groupe(resultats, "BANKER")
somme_player = calculer_somme_differentiels_groupe(resultats, "PLAYER")
```
"""
function calculer_somme_differentiels_groupe(resultats_simulation, groupe::String)
    # Validation des paramètres
    if groupe ∉ ["BANKER", "PLAYER"]
        throw(ArgumentError("groupe doit être 'BANKER' ou 'PLAYER', reçu: '$groupe'"))
    end

    somme_totale = 0.0

    for (index5, metriques) in resultats_simulation
        if occursin(groupe, index5)
            # Additionner les 8 différentiels
            somme_totale += metriques["Diff_CondT"]
            somme_totale += metriques["Diff_DivKLT"]
            somme_totale += metriques["Diff_CrossT"]
            somme_totale += metriques["Diff_MetricT"]
            somme_totale += metriques["Diff_TopoT"]
            somme_totale += metriques["Diff_TauxT"]
            somme_totale += metriques["Diff_ShannonT"]
            somme_totale += metriques["Diff_BlockT"]
        end
    end

    return somme_totale
end

"""
    predire_index3(predicteur::PredicteurDifferentiels, resultats_simulation) -> Tuple{String, Float64, Float64}

Prédit INDEX3 (BANKER ou PLAYER) basé sur la somme des différentiels la plus élevée.
Utilise le principe que le groupe avec la plus grande somme de différentiels
est plus susceptible d'être le résultat.

# Arguments
- `predicteur::PredicteurDifferentiels`: Instance du prédicteur
- `resultats_simulation`: Résultats de simulation avec métriques

# Returns
- `Tuple{String, Float64, Float64}`: (prédiction, somme_banker, somme_player)

# Exemples
```julia
prediction, somme_b, somme_p = predire_index3(predicteur, resultats)
```
"""
function predire_index3(predicteur::PredicteurDifferentiels, resultats_simulation)
    somme_banker = calculer_somme_differentiels_groupe(resultats_simulation, "BANKER")
    somme_player = calculer_somme_differentiels_groupe(resultats_simulation, "PLAYER")

    if somme_banker > somme_player
        return "BANKER", somme_banker, somme_player
    else
        return "PLAYER", somme_banker, somme_player
    end
end

"""
    mettre_a_jour_compteurs(predicteur::PredicteurDifferentiels, prediction_correcte::Bool)

Met à jour les compteurs de performance du prédicteur.
Maintient les statistiques de prédictions correctes/incorrectes et les séquences consécutives.

# Arguments
- `predicteur::PredicteurDifferentiels`: Instance du prédicteur à mettre à jour
- `prediction_correcte::Bool`: true si la prédiction était correcte

# Effets de Bord
Modifie les compteurs internes du prédicteur.
"""
function mettre_a_jour_compteurs(predicteur::PredicteurDifferentiels, prediction_correcte::Bool)
    if prediction_correcte
        predicteur.predictions_correctes[] += 1
        predicteur.correctes_consecutives[] += 1
        predicteur.incorrectes_consecutives[] = 0

        if predicteur.correctes_consecutives[] > predicteur.max_correctes_consecutives[]
            predicteur.max_correctes_consecutives[] = predicteur.correctes_consecutives[]
        end
    else
        predicteur.predictions_incorrectes[] += 1
        predicteur.incorrectes_consecutives[] += 1
        predicteur.correctes_consecutives[] = 0

        if predicteur.incorrectes_consecutives[] > predicteur.max_incorrectes_consecutives[]
            predicteur.max_incorrectes_consecutives[] = predicteur.incorrectes_consecutives[]
        end
    end
end

"""
    extraire_index3(index5::String) -> String

Extrait la composante INDEX3 d'une chaîne INDEX5.

# Arguments
- `index5::String`: Chaîne au format "INDEX1_INDEX2_INDEX3"

# Returns
- `String`: INDEX3 extrait ou "UNKNOWN" si format invalide

# Exemples
```julia
index3 = extraire_index3("1_A_BANKER")  # Retourne "BANKER"
```
"""
function extraire_index3(index5::String)
    if isempty(index5)
        return "UNKNOWN"
    end

    parties = split(index5, "_")
    if length(parties) >= 3
        return parties[3]
    else
        return "UNKNOWN"
    end
end

# ═══════════════════════════════════════════════════════════════════════════════
# POINT D'ENTRÉE DU PROGRAMME
# ═══════════════════════════════════════════════════════════════════════════════

"""
    __init__()

Fonction d'initialisation du module appelée automatiquement lors du chargement.
Vérifie que tous les modules de métriques sont correctement intégrés.
"""
function __init__()
    println("📦 Module PredicteurIndex5 chargé avec succès")
    println("🎯 Prêt pour les prédictions INDEX5 avec métriques théoriques")

    # Vérification de l'intégration des modules
    modules_status = []

    try
        # Test des modules autonomes
        push!(modules_status, ("CondT", isdefined(CondT, :calculer_formule5B_conditionnelle_theo)))
        push!(modules_status, ("DivKLT", isdefined(DivKLT, :calculer_formule6B_divergence_kl_theo)))
        push!(modules_status, ("CrossT", isdefined(CrossT, :calculer_formule8B_entropie_croisee_theo)))
        push!(modules_status, ("MetricT", isdefined(MetricT, :calculer_formule4B_entropie_metrique_theo)))
        push!(modules_status, ("TopoT", isdefined(TopoT, :calculer_formule9B_entropie_topologique_theo)))
        push!(modules_status, ("TauxT", isdefined(TauxT, :calculer_formule3B_taux_entropie_theo)))
        push!(modules_status, ("ShannonT", isdefined(ShannonT, :calculer_formule1B_shannon_jointe_theo)))
        push!(modules_status, ("BlockT", isdefined(BlockT, :calculer_formule10B_block_cumulative_theo)))

        # Affichage du statut
        println("🔧 Statut des modules de métriques :")
        for (nom, status) in modules_status
            status_icon = status ? "✅" : "❌"
            println("   $status_icon $nom")
        end

        all_loaded = all(status for (_, status) in modules_status)
        if all_loaded
            println("🎉 Tous les modules sont correctement intégrés !")
        else
            println("⚠️  Certains modules ne sont pas disponibles")
        end

    catch e
        println("⚠️  Erreur lors de la vérification des modules : $e")
    end
end

# Lancer le programme si exécuté directement
if abspath(PROGRAM_FILE) == @__FILE__
    exit(main())
end

# Fermeture du module
end # module PredicteurIndex5
