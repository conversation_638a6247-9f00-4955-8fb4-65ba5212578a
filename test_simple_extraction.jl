include("CacheJSON.jl")
using .CacheJSON

println("=== TEST SIMPLE EXTRACTION ===")

# Configuration simple
CacheJSON.configure_cache(
    max_parties = 100,
    max_files = 1,
    threading = true,
    stats = false
)

# Test extraction simple
println("Test extraction 10 parties individuellement...")
start_time = time()
for i in 1:10
    partie = CacheJSON.get_partie_cached(i)
    println("   Partie $i: $(length(partie.mains)) mains")
end
end_time = time()

total_time = end_time - start_time
speed = 10 / total_time

println("Résultat:")
println("   10 parties en: $(round(total_time, digits=3))s")
println("   Vitesse: $(round(speed, digits=1)) parties/sec")

if speed > 50
    println("🏆 EXCELLENT: >50 parties/sec")
elseif speed > 20
    println("✅ TRÈS BON: >20 parties/sec")
elseif speed > 10
    println("✅ BON: >10 parties/sec")
else
    println("⚠️  Performance de base: $(round(speed, digits=1)) parties/sec")
end
